source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
# gem "jbuilder"

gem "jwt"

gem "bcrypt"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin Ajax possible
gem "rack-cors"

# Use GeoKit for geolocation functionality
gem "geokit", require: true
gem "geokit-rails", "~> 2.5", require: true

# Use CarrierWave for file uploads
gem "carrierwave", "~> 3.0"

# Use Twilio for SMS verification
gem "twilio-ruby"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  gem "dotenv-rails"
end
# Grape API framework and related gems
gem "grape", "~> 2.3"
gem "grape-entity", "~> 1.0"
gem "grape-swagger", "~> 2.1"

# Pagination for Grape API
gem "kaminari", "~> 1.2"
gem "grape-kaminari", "~> 0.4"

gem "rspec-rails", "~> 8.0", groups: [ :development, :test ]
gem "factory_bot_rails", "~> 6.4", groups: [ :development, :test ]
gem "faker", "~> 3.5", groups: [ :development, :test ]
gem "database_cleaner-active_record", "~> 2.2", groups: [ :development, :test ]

group :development do
  # Use listen for file system change detection
  gem "listen", "~> 3.8"
  # Windows Directory Monitor to avoid polling for changes
  gem "wdm", ">= 0.1.0", platforms: [ :mingw, :mswin, :x64_mingw ]
end

group :test do
  gem "shoulda-matchers", "~> 5.0"
end

# Add devise for user authentication
gem "devise"

# Chat GPT helper
gem "chatgpt-ruby"

# Add figaro for managing environment variables
gem "figaro"

gem "rubocop", "~> 1.75"
