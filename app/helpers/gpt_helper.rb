# frozen_string_literal: true

module <PERSON>pt<PERSON>el<PERSON>
  require "chatgpt/client"
  require "json"
  require "active_record"

  def generate_gpt_auto_poll_response
    Rails.logger.info("Generating auto poll response...")
    polls = Poll.pluck(:question)
    # Convert the array of names to a comma-separated string
    comma_separated_string = polls.join(", ")
    prompt = "generate a question for a brewery to better understand their customer, make sure it is not the same as #{comma_separated_string}, don't include any explanations in your response, json format, params are question, for options it must be guid and option"
    Rails.logger.info("Prompt for auto poll: #{prompt}")
    generate_gpt_poll_response(prompt)
  end

  def generate_poll_questions(name)
    Rails.logger.info("Generating poll questions for brewery: #{name}")
    prompt = "generate some poll questions to help me better understand my customers at my brewery named #{name},  don't include any explanations in your response, json format, params are question and udid"
    response = generate_gpt_response(prompt)
    Rails.logger.info("Poll questions generated: #{response['choices']}")
    response["choices"]
  end

  def generate_standardized_poll_response(prompt)
    Rails.logger.info("Generating standardized poll response...")
    prompt = "#{prompt}, don't include any explanations in your response, json format, params are question, for options it must be guid and option"
    generate_gpt_poll_response(prompt)
  end

  def generate_gpt_poll_response(prompt)
    Rails.logger.info("Generating GPT poll response for prompt: #{prompt}")
    response = generate_gpt_response(prompt)
    Rails.logger.info("GPT poll response generated: #{response['choices']}")
    response["choices"]
  end

  def generate_gpt_response(prompt)
    Rails.logger.info("Generating GPT response for prompt: #{prompt}")
    api_key = ENV["GPT_KEY"]
    return nil if api_key.nil?

    begin
      client = ChatGPT::Client.new(api_key)
      messages = [
        {
          role: "user",
          content: prompt
        }
      ]

      # Start a chat
      response = client.chat(messages)
      Rails.logger.info("GPT response received: #{response}")
      response
    rescue StandardError => e
      Rails.logger.error("Error generating GPT response: #{e.message}")
      nil
    end
  end
end
