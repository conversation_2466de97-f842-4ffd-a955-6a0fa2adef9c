module Checkin<PERSON>elper
  extend ActiveSupport::Concern

  begin
    include Defaults
  rescue NameError
    Rails.logger.warn("API::V1::Defaults is missing. Please define it if you need shared API helpers.")
  end

  begin
    include CheckinErrors
  rescue NameError
    Rails.logger.warn("CheckinErrors module is missing. Please define it for error codes.")
  end

  def validate_facility_checkin(tokenInfo, facility)
    if facility.nil?
      Rails.logger.error("CheckinHelper: invalid facility id for checkin")
      error!({ message: "invalid facility id for checkin", code: defined?(CheckinErrors_Facility) ? CheckinErrors_Facility : 1001 }, 404)
    end

    validate_checkin_user_verified(tokenInfo)
    validate_checkin_location(tokenInfo, facility)
    validate_checkin_date_time(tokenInfo, facility)
  end

  def validate_trail_checkin(tokenInfo, facility, trail)
    if facility.nil?
      Rails.logger.error("CheckinHelper: invalid facility id for checkin")
      error!({ message: "invalid facility id for checkin", code: defined?(CheckinErrors_Facility) ? CheckinErrors_Facility : 1001 }, 404)
    end

    if trail.nil?
      Rails.logger.error("CheckinHelper: invalid trail id for checkin")
      error!({ message: "invalid trail id for checkin", code: defined?(CheckinErrors_Trail) ? CheckinErrors_Trail : 1002 }, 404)
    end

    if trail.verifiesUserIsVerifiedOnCheckin == true
      validate_checkin_user_verified(tokenInfo)
    end

    if trail.verifiesLocationOnCheckin == true
      validate_checkin_location(tokenInfo, facility)
    end

    if trail.verifiesTimeFromLastCheckin == true
      validate_checkin_date_time(tokenInfo, facility)
      validate_checkin_in_trail_time(tokenInfo, facility, trail)
    end
  end

  def validate_checkin_token(options = {})
    decoded = Base64.decode64(params[:token])
    info = JSON.parse(decoded) rescue nil
    info
  end

  def validate_checkin_user_verified(options = {})
    unless current_user.respond_to?(:verified?) && current_user.verified?
      Rails.logger.error("CheckinHelper: user not verified for checkin")
      error!({ message: "user not verified", code: defined?(CheckinErrors_User_Not_Verified) ? CheckinErrors_User_Not_Verified : 1003 }, 403)
    end
    true
  end

  def validate_checkin_facility(tokenInfo, facility)
    if tokenInfo.nil? || tokenInfo["id"] != facility.id
      Rails.logger.error("CheckinHelper: invalid facility id in token")
      error!({ message: "invalid facility id in token", code: defined?(CheckinErrors_Facility) ? CheckinErrors_Facility : 1001 }, 429)
    end

    if tokenInfo["code"] != facility.validcode
      Rails.logger.error("CheckinHelper: invalid code in token")
      error!({ message: "invalid code in token", code: defined?(CheckinErrors_Invalid_Code) ? CheckinErrors_Invalid_Code : 1004 }, 429)
    end
  end

  def validate_checkin_location(tokenInfo, facility)
    facilityLat = facility.latitude
    facilityLng = facility.longitude

    locations = Location.where(user_id: current_user.id).where("timestamp >= :min", min: DateTime.now - 6.minutes)

    if locations.empty?
      Rails.logger.error("CheckinHelper: no recent locations to validate")
      error!({ message: "no recent locations to validate", code: defined?(CheckinErrors_No_Locations) ? CheckinErrors_No_Locations : 1005 }, 429)
    end

    if locations.count < 30
      Rails.logger.error("CheckinHelper: not enough locations to validate")
      error!({ message: "not enough locations to validate", code: defined?(CheckinErrors_Not_Enough_Locations) ? CheckinErrors_Not_Enough_Locations : 1006 }, 429)
    end

    avgLat = locations.map(&:latitude).inject(0, :+) / locations.count
    avgLng = locations.map(&:longitude).inject(0, :+) / locations.count

    @origin = Geokit::LatLng.new(facility.latitude, facility.longitude)
    @average = Geokit::LatLng.new(avgLat, avgLng)
    distance = @average.distance_to(@origin, units: :kms)
    radius = facility.radius || 1.0
    if distance > radius
      Rails.logger.error("CheckinHelper: average distance is too large (#{distance} > #{radius})")
      error!({ message: "average distance is too large", code: defined?(CheckinErrors_Distance) ? CheckinErrors_Distance : 1007 }, 429)
    end
  end

  def validate_checkin_in_trail_time(tokenInfo, facility, trail)
    if tokenInfo["datetime"].to_datetime.today? == false
      Rails.logger.error("CheckinHelper: datetime is not today")
      error!({ message: "datetime is not today", code: defined?(CheckinErrors_Bad_Input_Date) ? CheckinErrors_Bad_Input_Date : 1008 }, 429)
    end

    now = DateTime.now
    unless now.after?(trail.startdate) && now.before?(trail.enddate)
      Rails.logger.error("CheckinHelper: outside of trail time")
      error!({ message: "outside of trail time", code: defined?(CheckinErrors_Bad_Input_Date) ? CheckinErrors_Bad_Input_Date : 1008 }, 429)
    end
  end

  def validate_checkin_date_time(tokenInfo, facility)
    if tokenInfo["datetime"].nil?
      Rails.logger.error("CheckinHelper: datetime is nil today")
      error!({ message: "datetime is nil today", code: defined?(CheckinErrors_Bad_Input_Date) ? CheckinErrors_Bad_Input_Date : 1008 }, 429)
    end

    lastCheckin = current_user.checkins.last
    if lastCheckin
      difference = (Time.now - Time.parse(lastCheckin.created_at.to_datetime.to_s)) / 1.minutes
      if difference < 15
        Rails.logger.error("CheckinHelper: too frequent checkins (#{difference} minutes since last)")
        error!({ message: "too frequent checkins", code: defined?(CheckinErrors_Frequency) ? CheckinErrors_Frequency : 1009 }, 429)
      end
      true
    else
      true
    end
  end
end
