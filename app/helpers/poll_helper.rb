module PollHelper

  def get_poll(facilityId)
    answered_poll_ids = current_user.user_responses.pluck(:poll_id)

    # Exclude answered polls from the list of all polls
    if facilityId.nil?
      unanswered_polls = Poll.order(created_at: :desc).limit(5).where.not(id: answered_poll_ids).where(:facility_id => nil)
    else
      unanswered_polls = Poll.where.not(id: answered_poll_ids).where(facility_id: params[:facilityId])
    end

    unanswered_polls.first
  end

  def create_facility_poll(choices, facility)
    poll = create_generic_poll(choices)
    poll.update_attribute(:facility_id, facility)
    poll.save!
    poll
  end
  def create_generic_poll(choices)
    option = choices.first

    if option.nil?
      puts "Failed to create poll"
      return
    end

    content = JSON.parse(option['message']['content'])

    if content.nil?
      puts "Failed to get poll content"
      return
    end

    # Create a new poll
    poll = Poll.create(question: content['question'])

    puts content
    # Create options associated with the poll
    content['options'].each do |option_data|
      poll.options.create(option: option_data['option'])
    end

    puts "Poll created with ID: #{poll.id}"
    poll
  end
end
