# app/api/entities/checkin.rb
module Entities
    class Checkin < Grape::Entity
      expose :id

      # Expose facility as its ID
      expose :facility do |checkin, _|
        checkin.facility_id
      end

      expose :created_at
      expose :user_id
      expose :trail_id

      # Only include full user info for vendors
      expose :user, using: Entities::SimpleUser, if: ->(_checkin, options) {
        options[:scope].current_user.isVendor
      } do |checkin, _|
        User.find_by(id: checkin.user_id)
      end
    end
end
