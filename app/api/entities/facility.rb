# app/api/entities/facility.rb
module Entities
    class Facility < Grape::Entity
      include Rails.application.routes.url_helpers

      # Basic attributes
      expose :id
      expose :name
      expose :handle
      expose :latitude
      expose :longitude
      expose :street
      expose :city
      expose :state
      expose :zipcode
      expose :website
      expose :facebook
      expose :instagram
      expose :twitter

      # Avatar URL
      expose :avatar do |facility, _|
        facility.avatar.url
      end

      # Cover image URL
      expose :cover do |facility, _|
        facility.cover.url
      end

      # Today’s check-in count
      expose :checkins_today do |facility, _|
        facility.checkins
                .where(created_at: Time.zone.now.beginning_of_day..Time.zone.now.end_of_day)
                .count
      end

      # Whether the current user is following
      expose :is_following do |facility, options|
        options[:scope].current_user.following?(facility)
      end

      # Total followers count
      expose :followers do |facility, _|
        facility.followers.count
      end

      # Vendor-only fields
      expose :team,   using: Entities::Team, if: ->(_f, opts) { opts[:scope].current_user.isVendor }
      expose :token,  if: ->(_f, opts) { opts[:scope].current_user.isVendor }
      expose :radius, if: ->(_f, opts) { opts[:scope].current_user.isVendor }
    end
end
