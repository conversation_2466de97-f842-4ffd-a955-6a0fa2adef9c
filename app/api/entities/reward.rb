# app/api/entities/reward.rb
module Entities
    class Reward < Grape::Entity
      include Rails.application.routes.url_helpers

      expose :id
      expose :trail_id
      expose :name
      expose :description
      expose :odds
      expose :is_single_reward
      expose :is_claimed

      # Avatar URL
      expose :avatar do |reward, _|
        reward.avatar.url
      end

      # Winner info, only when claimed_by is present
      expose :winner, using: Entities::SimpleUser, if: ->(reward, _opts) {
        reward.claimed_by.present?
      } do |reward, _|
        User.find_by_id(reward.claimed_by)
      end
    end
end
