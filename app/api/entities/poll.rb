# app/api/entities/poll.rb
module Entities
  class Poll < Grape::Entity
    expose :id, documentation: { type: "String", desc: "UUID of the poll" }
    expose :question, documentation: { type: "String", desc: "The poll question" }
    expose :facility_id, documentation: { type: "String", desc: "Associated Facility UUID (null for global polls)" }
    expose :created_at, documentation: { type: "DateTime", desc: "Poll creation timestamp" }
    expose :updated_at, documentation: { type: "DateTime", desc: "Poll last update timestamp" }
    
    # Expose poll options
    expose :options, using: Entities::Option, documentation: { type: "Array", desc: "Poll options" }
  end
end
