module Entities
  class UserPerk < Grape::Entity
    expose :id, documentation: { type: "String", desc: "UUID of the user_perk" }
    expose :perk_id, documentation: { type: "String", desc: "Associated Perk UUID" }
    expose :user_id, documentation: { type: "String", desc: "Associated User UUID" }
    expose :is_claimed, documentation: { type: "Boolean", desc: "Whether the perk was claimed by the user" }
    expose :code, documentation: { type: "Integer", desc: "Optional numeric code for the perk" }
    expose :created_at, documentation: { type: "DateTime", desc: "Record creation timestamp" }
    expose :updated_at, documentation: { type: "DateTime", desc: "Record last update timestamp" }

    # If you want to include associated models later:
    # expose :perk, using: Entities::PerkEntity
    # expose :user, using: Entities::UserEntity
  end
end
