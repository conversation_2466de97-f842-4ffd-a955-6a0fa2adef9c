# app/api/entities/perk_entity.rb
module Entities
  class Perk < Grape::Entity
    expose :id, documentation: { type: "String", desc: "UUID of the perk" }
    expose :facility_id, documentation: { type: "String", desc: "Associated Facility UUID" }
    expose :checkins_required, documentation: { type: "Integer", desc: "Number of check-ins required to unlock" }
    expose :startdate, documentation: { type: "DateTime", desc: "When the perk becomes active" }
    expose :enddate, documentation: { type: "DateTime", desc: "When the perk expires" }
    expose :title, documentation: { type: "String", desc: "Title/Name of the perk" }
    expose :reward, documentation: { type: "String", desc: "Description of the reward" }
    expose :avatar, documentation: { type: "String", desc: "Optional avatar image URL for the perk" }
    expose :created_at, documentation: { type: "DateTime", desc: "Record creation timestamp" }
    expose :updated_at, documentation: { type: "DateTime", desc: "Record last update timestamp" }

    # If you want to include associations:
    # expose :facility, using: Entities::FacilityEntity
    # expose :user_perks, using: Entities::UserPerkEntity
  end
end
