# app/api/entities/trail.rb
module Entities
    class Trail < Grape::Entity
      include Rails.application.routes.url_helpers

      # Basic fields
      expose :id
      expose :name
      expose :description
      expose :host
      expose :is_active
      expose :is_using_generic_code
      expose :startdate
      expose :enddate

      # Avatar URL
      expose :avatar do |trail, _|
        trail.avatar.url
      end

      # Whether the current user has joined this trail
      expose :has_joined do |trail, options|
        options[:scope].current_user.joined?(trail)
      end

      # Total checkins count
      expose :checkins do |trail, _|
        trail.checkins.count
      end

      # Full checkin objects for “sippers”
      expose :checkins_complete,
             using: Entities::Checkin,
             if:    ->(trail, options) { options[:scope].current_user.isSipper } do |trail, options|
        records = Checkin.where(
          user_id:    options[:scope].current_user.id,
          trail_id:   trail.id,
          created_at: trail.startdate...trail.enddate
        )
        # one per facility
        records.uniq { |c| c.facility_id }
      end

      # Boolean: did sipper complete all facilities?
      expose :all_checkins_complete,
             if: ->(trail, options) { options[:scope].current_user.isSipper } do |trail, options|
        records = Checkin.where(
          user_id:    options[:scope].current_user.id,
          trail_id:   trail.id,
          created_at: trail.startdate...trail.enddate
        )
        unique_count = records.uniq { |c| c.facility_id }.size
        unique_count == trail.facilities.size
      end

      # Only visible to vendors
      expose :token,
             if: ->(_trail, options) { options[:scope].current_user.isVendor }

      # Associations
      expose :facilities, using: Entities::Facility
      expose :rewards,    using: Entities::Reward
    end
end
