# app/api/entities/simple_user.rb
module Entities
    class SimpleUser < Grape::Entity
      include Rails.application.routes.url_helpers

      expose :id
      expose :role
      expose :verified
      expose :username

      # Avatar URL
      expose :avatar do |user, _|
        # Ensure you’ve configured default_url_options[:host] if you need a full URL
        user.avatar.url
      end
    end
end
