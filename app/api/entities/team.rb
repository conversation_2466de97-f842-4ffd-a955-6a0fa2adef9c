# app/api/entities/team.rb
module Entities
    class Team < Grape::Entity
      expose :id
      expose :name

      # Leader as a simplified user entity
      expose :leader, using: Entities::SimpleUser do |team, _|
        team.leader
      end

      # Members as an array of simplified user entities
      expose :members, using: Entities::SimpleUser do |team, _|
        team.users
      end

      # Just the facility ID
      expose :facility do |team, _|
        team.facility&.id
      end
    end
end
