# app/api/entities/option.rb
module Entities
  class Option < Grape::Entity
    expose :id, documentation: { type: "String", desc: "UUID of the option" }
    expose :option, documentation: { type: "String", desc: "The option text" }
    expose :poll_id, documentation: { type: "String", desc: "Associated Poll UUID" }
    expose :created_at, documentation: { type: "DateTime", desc: "Option creation timestamp" }
    expose :updated_at, documentation: { type: "DateTime", desc: "Option last update timestamp" }
  end
end
