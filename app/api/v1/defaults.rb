    module V1
      module Defaults
        extend ActiveSupport::Concern

        included do
          # Prefix and version are set at the top-level API (see `app/api/api.rb`).
          # Defining them here leads to double-prefixed routes like `/api/v1/api/v1/...`.
          # Keep response format settings local to mounted APIs.
          default_format :json
          format :json


          helpers do
            include Devise::Controllers::Helpers

            def permitted_params
              @permitted_params ||= declared(params,
                                             include_missing: false)
            end

            def logger
              Rails.logger
            end

            def authenticate_user!(options = {})
              process_token
              error!({ message: "401 Unauthorized token - unable to find headers", code: 401 }, 401) unless signed_in?
            end

            def verify_vendor!
              error!({ message: "403 Unauthorized only vendors can access", code: 403 }, 403) unless current_user.isVendor || current_user.isAdmin
            end

            def verified!
              error!({ message: "403 user is not verified", code: 1001 }, 403) unless current_user.verified
            end

            def signed_in?
              @current_user_id.present?
            end

            def current_user
              @current_user ||= User.find(@current_user_id)
            end

            def process_token
              puts request.headers
              if request.headers["Authorization"].present?
                begin
                  jwt_payload = JWT.decode(request.headers["Authorization"].split(" ")[1].remove('"'), Rails.application.secret_key_base).first
                  @current_user_id = jwt_payload["id"]
                rescue JWT::ExpiredSignature, JWT::VerificationError, JWT::DecodeError => e
                  puts e.message
                  puts e.backtrace.join("\n")
                  error!({ message: "401 Unauthorized token", code: 401 }, 401)
                end
              end
            end
          end

          rescue_from ActiveRecord::RecordNotFound do |e|
            error_response(message: e.message, status: 404)
          end

          rescue_from ActiveRecord::RecordInvalid do |e|
            error_response(message: e.message, status: 422)
          end
        end
      end
    end
