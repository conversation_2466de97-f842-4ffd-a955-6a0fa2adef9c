module V1
  class SettingsUser < Grape::API
    include Defaults

    before {
      authenticate_user!
    }

    resource :settings_user do
      desc 'update a sippers profile'
      params do
        optional :gender, type: Integer
        optional :phone, type: String
        optional :device_token, type: String
        optional :avatar, :type => Rack::Multipart::UploadedFile, :desc => "Sipper profile image."
      end
      put do
        current_user.update(declared(params, include_missing: false))
        present current_user, with: Entities::User, scope: self
      end
    end
  end
end
