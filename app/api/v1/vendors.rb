module V1
    class Vendors < Grape::API
        include Defaults
        ERROR_CODES = ::Constants::VendorErrors
        ERROR_CODES_FACILITY = ::Constants::FacilityErrors
        ERROR_CODES_SAVE = ::Constants::SaveErrors

      resource :vendors do
        desc "add owner to facility"
        params do
          requires :facility_id, type: String
        end
        put :set_owner do
          authenticate_user!
          verify_vendor!
          @facility = Facility.find_by(id: params[:facility_id])
          if @facility == nil
            return error!({ message: "Facility not found", code: ERROR_CODES_FACILITY::FacilityErrors_NotFound }, 401)
          end
          if @facility.user != nil
            return error!({ message: "Facility already has owner", code: ERROR_CODES::VendorErrors_FacilityAlreadyHasOwner }, 401)
          end
          current_user.facility = @facility

          @team = Team.find_by(name: @facility.name)
          if @team == nil
            @team = Team.create(name: @facility.name)
            if !@team.save!
              return error!({ message: "Failed to create a new team", code: ERROR_CODES_SAVE::SaveErrors_FailedToSaveInfo }, 401)
            end
          end

          current_user.team = @team
          if current_user.save!
            present current_user, with: Entities::User, scope: self
          else
            return error!({ message: "Failed to update user info", code: ERROR_CODES_SAVE::SaveErrors_FailedToSaveInfo }, 401)
          end
        end
      end
    end
end
