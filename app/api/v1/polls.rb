
  module V1
    class Polls < Grape::API
      helpers GptHelper
      helpers PollHelper
      include Defaults

      before {
        authenticate_user!
        verified!
      }

      resource :polls do
        desc "delete a poll"
        params do
          requires :pollId, type: String
        end
        delete do
          poll = Poll.find_by_id(params[:pollId])
          if poll.nil?
            error!({ error: "Poll does not exist", code: 99999}, 404)
          end
          if !poll.facility.nil? && poll.facility != current_user.team.facility
            error!({ error: "You do not have permission to delete this poll", code: 99999}, 404)
          end

          if poll.facility.nil? && !current_user.isAdmin
            error!({ error: "You do not have permission to delete this poll (admin)", code: 99999}, 404)
          end
          poll.destroy
        end

        desc "get a poll"
        params do
          optional :facilityId, type: String
        end
        get :poll do
          answered_poll_ids = current_user.user_responses.pluck(:poll_id)

          # Exclude answered polls from the list of all polls
          if params[:facilityId].nil?
            unanswered_polls = Poll.order(created_at: :desc).limit(5).where.not(id: answered_poll_ids).where(:facility_id => nil)
          else
            unanswered_polls = Poll.where.not(id: answered_poll_ids).where(facility_id: params[:facilityId])
          end

          unanswered_polls.first
        end

        desc "create a new poll with question and options"
        params do
          requires :question, type: String
          requires :options, type: Array
        end
        post :create_poll do
          verify_vendor!
          # Create a new poll
          poll = Poll.create(question: params[:question])

          params[:options].each do |option_data|
            poll.options.create(option: option_data[:option])
          end

          poll.update_attribute(:facility_id, current_user.team.facility.id)
          poll.save!
          present poll, with: Entities::Poll, scope: self
        end

        desc "create a new poll from prompt"
        params do
          requires :prompt, type: String
          requires :facilityId, type: String
        end
        post :create_with_prompt do
          verify_vendor!
          choices = generate_standardized_poll_response(params[:prompt])
          if choices.nil?
            error!({ error: "Failed to generate poll options from promp", code: 99999}, 404)
          end
          poll = create_facility_poll(choices, params[:facilityId])
          if poll.nil?
            error!({ error: "Failed to generate poll from promot", code: 99999}, 404)
          end
          present poll, with: Entities::Poll, scope: self
        end

        desc "Generate poll questions for my facility"
        get :generate_questions do
          verify_vendor!
          choices = generate_poll_questions(current_user.team.facility.name)
          option = choices.first

          if option.nil?
            puts "Failed to get questions"
            error!({ error: "Failed to generate questions", code: 99999}, 404)
          end

          content = JSON.parse(option['message']['content'])
          content
        end
      end
    end
  end
