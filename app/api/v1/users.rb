    module V1
      class Users < Grape::API
        ERROR_CODES = ::Constants::SignUpErrors
        include Defaults
        resource :users do
          desc "Upload Locations",
                security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            requires :locations, type: Array
          end
          put :locations do
            authenticate_user!
            locations = []
            params[:locations].each do |location|
              time = Time.now.utc
              locations << { "timestamp" => location[:timestamp].to_datetime,
                 "latitude" => location[:latitude],
                 "longitude" => location[:longitude],
                 "user_id" => current_user.id,
                 "created_at" => time,
                 "updated_at" => time  }
            end
            Location.insert_all(locations)
            render status: :ok
          end

          desc "Return a user",
            security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            requires :id, type: String, desc: "ID of the user"
          end
          get :id do
            authenticate_user!
            User.where(id: permitted_params[:id]).first!
          end

          desc "Get the users rewards they have won",
                security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            optional :per_page, type: Integer, default: 25
            optional :page, type: Integer, default: 1
          end
          get :rewards do
            authenticate_user!
            paginate current_user.rewards.order(created_at: :desc).paginate(page: params[:page])
          end

          desc "Get the users trails they have joined",
                security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            optional :per_page, type: Integer, default: 25
            optional :page, type: Integer, default: 1
          end
          get :trails do
            authenticate_user!
            trails = current_user.trails.order(startdate: :desc).page(params[:page]).per(params[:per_page])
            paginate trails

            present trails,
                with:  Entities::Trail,
                scope: self
          end

          desc "Get the users checkins",
                security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
            params do
                optional :page,     type: Integer, default: 1,   desc: "Page number (starts at 1)"
                optional :per_page, type: Integer, default: 25,  desc: "Items per page"
            end

          get :checkins do
            authenticate_user!
            # build a paginated AR::Relation
            checkins = current_user.checkins.order(created_at: :desc).page(params[:page]).per(params[:per_page])

            paginate checkins

            present checkins,
                with:  Entities::Checkin,
                scope: self
          end

          desc "Log in"
          params do
            requires :password, type: String
            requires :email, type: String
          end
          put :log_in do
            user = User.find_by_email(params[:email].downcase)
            if user && user.valid_password?(params[:password])
              token = user.generate_jwt

              env["warden"].set_user(user, scope: :user)

              TryDetermineLatLngForUser.perform_later(user.id, request.env["REMOTE_ADDR"])
              present user, with:   Entities::User, email: params[:email], token: token
            else
              error!({ message: "Invalid email or password", code: ERROR_CODES::SignUpErrors_ErrorInvalidCredentials }, 404)
            end
          end

          desc "Sign up"
          params do
            requires :username, type: String
            requires :birthdate, type: String, desc: "the birthdate of the user 21 and over"
            requires :email, type: String
            requires :password, type: String
            requires :role, type: Integer, values: [ 0, 1 ]
            requires :gender, type: Integer
            requires :tos, type: Boolean
          end
          post :sign_up do
            @user = User.create(declared(params))
            if @user.save
              present @user, with: Entities::User, scope: self
            else
              first_attr = @user.errors.attribute_names.first.to_s
              first_error = @user.errors.full_messages.first.to_s
              case first_attr
              when "email"
                error!({ message: first_error, code: ERROR_CODES::SignUpErrors_ErrorCodeEmail }, 404)
              else
                error!({ message: "some error : " + @user.errors.full_messages, code: ERROR_CODES::SignUpErrors_ErrorCodeUnknown }, 404)
              end
            end
          end
        end
      end
    end
