module V1
  class SettingsVendor < Grape::API
    include Defaults
    ERROR_CODES_SAVE = ::Constants::SaveErrors
    ERROR_CODES_TEAM = ::Constants::TeamErrors

    before {
      authenticate_user!
      verify_vendor!
    }

    resource :settings_vendor do

      desc 'accept team invitation'
      params do
        requires :team_id, type: String
      end
      post :accept_invite do
        authenticate_user!
        verify_vendor!
        @team = Team.find_by_id(params[:team_id])
        if @team == nil
          return error!({ message: "Team not found", code: ERROR_CODES_TEAM::TeamErrors_NotFound }, 401)
        end
        current_user.team = @team
        if current_user.save!
          present current_user, with: Entities::User, scope: self
        else
          return error!({ message: "Failed to update user info", code: ERROR_CODES_SAVE::SaveErrors_FailedToSaveInfo }, 401)
        end
      end

      desc "update vendor settings"
      params do
        optional :avatar, :type => Rack::Multipart::UploadedFile, :desc => "Facility profile image."
        optional :cover, :type => Rack::Multipart::UploadedFile, :desc => "Facility cover image."
        optional :name, :type => String, :desc => "Name for facility"
        optional :handle, :type => String, :desc => "The handle for the facility"
        optional :phone, :type => String, :desc => "The phone for the facility"
        optional :street, :type => String, :desc => "The street for the facility"
        optional :city, :type => String, :desc => "The city for the facility"
        optional :state, :type => String, :desc => "The state for the facility"
        optional :zipcode, :type => String, :desc => "The zipcode for the facility"
        optional :website, :type => String, :desc => "The website for the facility"
        optional :facebook, :type => String, :desc => "The facebook for the facility"
        optional :instagram, :type => String, :desc => "The instagram for the facility"
        optional :twitter, :type => String, :desc => "The twitter for the facility"
        optional :latitude, :type => String, :desc => "The latitude for the facility"
        optional :longitude, :type => String, :desc => "The longitude for the facility"
        optional :radius, :type => String, :desc => "The checkin radius for the facility"
      end
      post do
        authenticate_user!
        verify_vendor!

        if current_user.team.nil? || current_user.team.facility.nil?
          return error!({ message: "Failed to get facility", code: ERROR_CODES_SAVE::SaveErrors_FailedToSaveInfo }, 401)
        end

        facility = current_user.team.facility
        unless facility.update(declared(params, include_missing: false))
          return error!({ message: facility.errors.objects.first.full_message, code: ERROR_CODES_SAVE::SaveErrors_FailedToSaveInfo}, 404)
        end
        present facility, with: Entities::Facility, scope: self
      end
    end
  end
end
