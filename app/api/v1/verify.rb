    module V1
      class Verify < Grape::API
        include Defaults
        ERROR_CODES = ::Constants::VerifyErrors

        resource :verify do
          desc "Get new token",
            security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            requires :phonenumber, type: String
          end
          get :new_token do
            authenticate_user!

            @existing = User.find_by(phone: params[:phonenumber])
            if Rails.env.production? && @existing != nil
              return error!({ message: "Invalid phone number", code: ERROR_CODES::VerifyErrors_PhoneInUse }, 404)
            end

            current_user.update_attribute(:phone, params[:phonenumber])
            current_user.create_verification
            account_sid = ENV["TWILIO_SID"]
            auth_token = ENV["TWILIO_TOKEN"]
            client = Twilio::REST::Client.new(account_sid, auth_token)

            from = ENV["TWILIO_PHONE"] # Your Twilio number
            to = current_user.phone # Your mobile phone number

            client.messages.create(
                from: from,
                to: to,
                body: "Here is your SipTrails verification code. Cheers! 🍻  #{current_user.verification.code}"
            )
            render status: :ok
          end

          desc "User Verify",
            security: [ { bearerAuth: [] } ],
                headers: {
                    "Authorization" => {
                        description: "Bearer <JWT>",
                        required:    true
                    }
                }
          params do
            requires :code, type: Integer
          end
          put :user_verify do
            authenticate_user!
            @code = Verification.find_by_user_id(current_user.id)
            if @code != nil && @code.code == params[:code]
              current_user.verify
              render current_user
            else
              error!({ message: "Invalid verification code", code: ERROR_CODES::VerifyErrors_InvalidToken }, 404)
            end
          end
        end
      end
    end
