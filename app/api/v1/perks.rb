  module V1
    class Perks < Grape::API
      include Defaults
      helpers <PERSON><PERSON><PERSON>el<PERSON>

      before {
        authenticate_user!
        verified!
      }

      resource :perks do
        desc "Delete a perk"
        params do
          requires :perk, type: String
        end
        delete do
          verify_vendor!
          perk = current_user.team.facility.perks.find_by_id(params[:perk])
          if perk == nil
            error!({ message: "Failed to find a perk", code: Constants::PerkErrors::PerkErrors_NotFound }, 403)
          end
          UserPerk.where(:perk_id => perk.id).destroy_all
          Perk.destroy(perk.id)
        end

        desc "Claim a perk and mark it"
        params do
          requires :token, type: String
        end
        put :claim do
          verify_vendor!
          tokenInfo = validate_checkin_token(params)
          userPerk = UserPerk.find_by_id(tokenInfo["id"])
          if userPerk == nil
            return error!({ message: "Failed to find a perk", code: Constants::PerkErrors::PerkErrors_NotFound }, 403)
          end
          if userPerk.is_claimed
            error!({ message: "This perk is already claimed", code: Constants::PerkErrors::PerkErrors_AlreadyClaimed }, 403)
          end
          if userPerk.perk.facility_id != current_user.team.facility.id
            error!({ message: "Your team dose not own this perk", code: Constants::PerkErrors::PerkErrors_NotOwned }, 403)
          end
          isValid = userPerk.code == tokenInfo["code"].to_i
          if isValid == false
            error!({ message: "invalid code for token and perk", code: Constants::PerkErrors::PerkErrors_InvalidCode }, 403)
          end
          userPerk.update(:is_claimed => true)
          NotifyOfPerkClaimJob.perform_later(userPerk.id)
          present userPerk, with: Entities::UserPerk, scope: self
        end

        desc "Get all perks current users facility"
        params do
          requires :id, type: String
          optional :per_page, type: Integer, default: 10
          optional :page, type: Integer, default: 1
        end
        get :all do
          facility = Facility.find_by_id(params[:id])
          if facility == nil
            error!({ message: "Failed to find a facility", code: Constants::FacilityErrors::FacilityErrors_NotFound }, 403)
          end
          paginate facility.perks.order(updated_at: :desc)
        end

        desc "Get my perks"
        params do
          optional :per_page, type: Integer, default: 10
          optional :page, type: Integer, default: 1
        end 
        get :mine do
          present paginate(current_user.user_perks.order(updated_at: :desc)), with: Entities::UserPerk, scope: self
        end

        desc "Create a new perk for a facility"
        params do
          requires :startdate, type: DateTime, allow_blank: false
          requires :enddate, type: DateTime, allow_blank: false
          requires :checkins_required, type: Integer, allow_blank: false
          requires :title, type: String, allow_blank: false
          requires :reward, type: String, allow_blank: false
          optional :avatar, :type => Rack::Multipart::UploadedFile, :desc => "Perk avtar image."
        end
        post :create do
          verify_vendor!
          declaredParams = declared(params)
          declaredParams[:facility_id] = current_user.team.facility.id
          perk = Perk.create(declaredParams)
          if perk.save!
            NotifyOfNewPerkJob.perform_later(current_user.team.facility.id, perk.id)
            present perk, with: Entities::Perk, scope: self
          else
            error!({ message: "Failed to create new perk", code: Constants::PerkErrors::PerkErrors_CreationFailed }, 403)
          end
        end

        desc "Edit an existing perk"
        params do
          requires :id, type: String
          optional :startdate, type: DateTime
          optional :enddate, type: DateTime
          optional :checkins_required, type: Integer
          optional :title, type: String
          optional :reward, type: String
          optional :avatar, :type => Rack::Multipart::UploadedFile, :desc => "Perk avtar image."
        end
        patch :edit do
          verify_vendor!
          perk = Perk.find_by_id(params[:id])
          if perk == nil
            error!({ message: "Failed to find perk for id", code: Constants::PerkErrors::PerkErrors_NotFound }, 403)
          end
          perk.update(declared(params, include_missing: false))
          present perk, with: Entities::Perk, scope: self
        end
      end
    end
  end

