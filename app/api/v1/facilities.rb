module V1
  class Facilities < Grape::API
    include Defaults
    ERROR_CODES = ::Constants::FacilityErrors

    before { authenticate_user! }

    resource :facility do
      desc "get a facility with id"
      params do
        requires :id, type: String
      end
      get do
        facility = Facility.find_by_id(params[:id])
        if facility != nil
          present facility, with: Entities::Facility, scope: self
        else
          error!({ message: "Facility not found", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
        end
      end

        desc "get a facility with team id"
        params do
          requires :team_id, type: String
        end
        get :team do
          verify_vendor!

          facility = Facility.find_by(team_id: params[:team_id])
          if facility != nil
            present facility, with: Entities::Facility, scope: self
          else
            error!({ message: "Facility not found", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
          end
        end

        desc "get facility near location by popular"
        params do
          optional :radius, type: Integer
          requires :lat, type: Float
          requires :lng, type: Float
        end
        get :near_me_popular do
          radius = params[:radius] || 15
          @facilities = Facility.within(radius, :units => :kms, :origin => [params[:lat], params[:lng]])
          facilities = @facilities.sort { |a,b| b.followers.count <=> a.followers.count }.first(5)
          present facilities, with: Entities::Facility, scope: self
        end

        desc "search for a facility by name"
        params do
          requires :term, type: String
          optional :page, type: Integer, default: 1, desc: 'Page number'
          optional :per_page, type: Integer, default: 25, desc: 'Items per page'
        end
        get :search do
          verified!
          results = paginate Facility.where('lower(name) LIKE ?', "%#{params[:term].downcase}%")
          present results, with: Entities::Facility, scope: self
        end

        desc "get rewards for a facility"
        params do
          optional :limit, type: Integer, default: 10
          optional :page, type: Integer, default: 0
        end
        get :rewards do
          verify_vendor!
          trails = paginate current_user.team.facility.trails
          present trails.map { |t| t.rewards }.flatten
        end

        desc "get facility checkins"
        params do
          optional :per_page, type: Integer, default: 10, desc: 'The limit or count of object to return per query, default = 10'
          optional :page, type: Integer, default: 0, desc: 'The current page in pagination, default = 0'
        end
        get :checkins do
          verify_vendor!
          present paginate(current_user.team.facility.checkins.order(created_at: :desc))
        end

        desc "follow multiple facilities"
        params do
          requires :facility_ids, type: Array
        end
        put :follow_all do
          verified!
          params[:facility_ids].each { |id|
            facility = Facility.find_by_id(id)
            if facility == nil
              error!({ message: "Facility not found", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
            end

            if !current_user.following?(facility)
              ActionCable.server.broadcast("checkins_#{facility.id}", {username: current_user.username, avatar: current_user.avatar.url, event: "follow"})
              current_user.follow(facility)
            end
          }
          status 200
          present({ ok: true })
        end

      desc "follow a facility"
      params do
        requires :facility_id, type: String
      end
      put :follow do
        verified!
        facility = Facility.find_by_id(params[:facility_id])
        if facility == nil
          error!({ message: "Facility not found", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
        end

        if current_user.following?(facility)
          error!({ message: "Already following this facility", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
        end
        current_user.follow(facility)
        ActionCable.server.broadcast("checkins_#{facility.id}", {username: current_user.username, avatar: current_user.avatar.url, event: "follow"})
        status 200
        present({ ok: true })
      end

        desc "unfollow a facility"
         params do
          requires :facility_id, type: String
        end
        put :unfollow do
          verified!
          facility = Facility.find_by_id(params[:facility_id])
          if facility == nil
            error!({ message: "Facility not found", code: ERROR_CODES::FacilityErrors_NotFound }, 403)
          end
          current_user.unfollow(facility)
          status 200
          present({ ok: true })
        end
    end
  end
end
