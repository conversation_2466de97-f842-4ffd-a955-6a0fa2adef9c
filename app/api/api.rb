class API < Grape::API
  prefix "api"
  version "v1", using: :path
  format :json

  # make paginate, page, per, etc. available everywhere
  include Grape::Kaminari

  helpers do
      params :pagination do
        optional :page, type: Integer, default: 1, desc: 'Page number'
        optional :per_page, type: Integer, default: 25, desc: 'Items per page'
      end
  end

  mount V1::Base

  add_swagger_documentation(
    api_version: "v1",
    hide_documentation_path: false,
    mount_path: "/swagger_doc",
    security_definitions: {
    bearerAuth: {
      type: "apiKey",
      name: "Authorization",
      in:   "header",
      description: "Enter your bearer token in the format **Bearer &lt;token>**"
    }
  },
    hide_format: true,
    info: {
      title:       "SipTrails API",
      description: "A simple JSON API built with Grape, documented with swagger"
    }
  )
end
