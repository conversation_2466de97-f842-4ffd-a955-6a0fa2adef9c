require "ipaddr"

class TryDetermineLatLngForUser < ApplicationJob
  queue_as :default

  def perform(user_id, ip_address)
    Rails.logger.info "[TryDetermineLatLngForUser] Starting job for user_id=#{user_id}, ip_address=#{ip_address}"

    # Validate IP format
    begin
      IPAddr.new(ip_address)
    rescue IPAddr::InvalidAddressError
      Rails.logger.warn "[TryDetermineLatLngForUser] Invalid IP address format: #{ip_address}, aborting"
      return
    end

    user = User.find_by_id(user_id)
    unless user
      Rails.logger.warn "[TryDetermineLatLngForUser] No user found with id=#{user_id}, aborting"
      return
    end

    if user.last_sign_in_ip == ip_address && user.latitude.present?
      Rails.logger.info "[TryDetermineLatLngForUser] Skipping: same IP and already have lat/lng (#{user.latitude},#{user.longitude})"
      return
    end

    Rails.logger.info "[TryDetermineLatLngForUser] Looking up geo for IP #{ip_address}"
    geo = ::Geokit::Geocoders::MultiGeocoder.geocode(ip_address)

    unless geo.success
      Rails.logger.warn "[TryDetermineLatLngForUser] GeoKit returned no results for IP #{ip_address}"
      return
    end

    lat, lng = geo.lat, geo.lng
    Rails.logger.info "[TryDetermineLatLngForUser] Got geo: latitude=#{lat}, longitude=#{lng}"

    # Validate lat/lng ranges
    unless lat.is_a?(Numeric) && lng.is_a?(Numeric) &&
           lat.between?(-90, 90) && lng.between?(-180, 180)
      Rails.logger.warn "[TryDetermineLatLngForUser] Invalid coordinates: latitude=#{lat}, longitude=#{lng}, aborting update"
      return
    end

    Rails.logger.info "[TryDetermineLatLngForUser] Updating user with new lat/lng and IP"
    if user.update(latitude: lat, longitude: lng, last_sign_in_ip: ip_address)
      Rails.logger.info "[TryDetermineLatLngForUser] Successfully updated user_id=#{user_id}"
    else
      Rails.logger.error "[TryDetermineLatLngForUser] Failed to update user_id=#{user_id}: #{user.errors.full_messages.join(', ')}"
    end

  rescue StandardError => e
    Rails.logger.error "[TryDetermineLatLngForUser] ERROR for user_id=#{user_id}, ip_address=#{ip_address}: #{e.class} – #{e.message}"
    raise
  end
end
