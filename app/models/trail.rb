class Trail < ApplicationRecord
  acts_as_mappable defaults_units: :kms,
                   defaults_formula: :sphere,
                   lat_column_name: :latitude,
                   lng_column_name: :longitude

  has_and_belongs_to_many :facilities
  has_and_belongs_to_many :users, -> { distinct }
  has_many :rewards, dependent: :delete_all
  has_many :checkins

  after_save :generate_token_if_needed
  after_create :schedule_determine_winner_job

  mount_uploader :avatar, AvatarUploader

  validates :name, :startdate, :enddate, presence: true

  def verifiesLocationOn<PERSON><PERSON>ckin
    should_verify_location
  end

  def verifiesTimeFromLast<PERSON>heckin
    should_verify_time_between
  end

  def verifiesUserIsVerifiedOn<PERSON>heckin
    should_verify_user_verified
  end

  def hostFacility
    Facility.find_by_id(host)
  end

  def schedule_determine_winner_job
    TrailDetermineWinnerJob.set(wait_until: enddate).perform_later(id)
  end

  def generate_token_if_needed
    if code == nil
      update_attribute(:code, rand(1000000...9999999))
      code
    end
  end

  def token
    json = { code: code, trail_id: id }.to_json
    Base64.strict_encode64(json)
  end
end
