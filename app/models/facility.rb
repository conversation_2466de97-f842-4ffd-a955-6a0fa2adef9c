class Facility < ApplicationRecord
  acts_as_mappable defaults_units: :kms,
                   defaults_formula: :sphere,
                   lat_column_name: :latitude,
                   lng_column_name: :longitude

  has_and_belongs_to_many :trails
  belongs_to :team, optional: true
  belongs_to :user, optional: true

  mount_uploader :avatar, AvatarUploader
  mount_uploader :cover, AvatarUploader

  has_many :perks, dependent: :destroy
  has_many :checkins
  has_many :active_relationships, class_name:  "FacilityRelationship",
                                  foreign_key: "facility_id",
                                  dependent:   :destroy
  has_many :followers, through: :active_relationships, source: :follower

  validates :handle,    uniqueness: true

  def validcode
    date = Time.now
    if code == nil
      update_attribute(:code, rand(1000000...9999999))
      update_attribute(:last_code_update, date)
      return code
    end

    difference = (date - last_code_update)
    if difference > 1.year
      update_attribute(:code, rand(1000000...9999999))
      update_attribute(:last_code_update, date)
    end
    code
  end

  def token
    json = { code: validcode, facility_id: id }.to_json
    Base64.strict_encode64(json)
  end

  def isOwned
    team != nil
  end
end
