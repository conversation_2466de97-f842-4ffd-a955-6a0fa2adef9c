class User < ApplicationRecord
  include ActiveModel::Serialization
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :trackable

  has_one :verification, dependent: :destroy
  has_one :facility
  belongs_to :team, optional: true
  has_and_belongs_to_many :trails, -> { distinct }
  has_many :checkins, dependent: :destroy
  has_many :locations, dependent: :destroy
  has_many :rewards, foreign_key: "claimed_by", class_name: "Reward"
  has_many :user_perks, dependent: :destroy
  has_many :user_responses, dependent: :destroy

  mount_uploader :avatar, AvatarUploader

  has_many :facility_relationships, class_name:  "FacilityRelationship",
                                  foreign_key: "follower_id",
                                  dependent:   :destroy
  has_many :following_facilities, through: :facility_relationships, source: :facility

  def generate_jwt
    JWT.encode({ id: id, exp: 24.hours.from_now.to_i }, Rails.application.secret_key_base)
  end

  def isFollowingAFacility
    following_facilities.count != 0
  end

  def isSipper
    role == 0
  end

  def isVendor
    role == 1
  end

  def isAdmin
    role == 2
  end

  def admin?
    isAdmin
  end

  def verify
    update_attribute(:verified, true)
    Verification.delete(verification.id)
  end

  def create_verification
    verification = Verification.find_by_user_id(id)
    if verification != nil
      verification.update_attribute(:code, rand(100000...999999))
    else
      Verification.create(code: rand(100000...999999), user_id: id).save
    end
  end

  def addReward(reward)
    reward.update_attribute(:claimed_by, id)
  end

  # Follows a facility.
  def follow(facility)
    facility_relationships.create(facility_id: facility.id)
  end

  # Unfollows a facility.
  def unfollow(facility)
    facility_relationships.find_by(facility_id: facility.id).destroy
  end

  # Returns true if the current user is following the facility.
  def following?(facility)
    following_facilities.include?(facility)
  end

  def joined?(trail)
    trails.include?(trail)
  end
end
