namespace :db do
  desc "Setup all database environments (development, test, staging, production)"
  task setup_all: :environment do
    puts "Setting up all database environments..."

    # Development environment
    Rake::Task["db:setup_development"].invoke

    # Test environment
    Rake::Task["db:setup_test"].invoke

    # Staging environment (if configured)
    if Rails.env.staging? || ENV["SETUP_STAGING"] == "true"
      Rake::Task["db:setup_staging"].invoke
    end

    # Production environment (if configured)
    if Rails.env.production? || ENV["SETUP_PRODUCTION"] == "true"
      Rake::Task["db:setup_production"].invoke
    end

    puts "All database environments have been set up successfully!"
  end

  desc "Setup development database"
  task setup_development: :environment do
    puts "Setting up development database..."

    # Store current environment
    current_env = Rails.env

    # Switch to development environment
    Rails.env = "development"

    # Create, migrate, and seed the database
    Rake::Task["db:drop"].invoke if ENV["FORCE_DROP"] == "true"
    Rake::Task["db:create"].invoke
    Rake::Task["db:schema:load"].invoke
    Rake::Task["db:seed"].invoke

    # Restore original environment
    Rails.env = current_env

    puts "Development database setup complete!"
  end

  desc "Setup test database"
  task setup_test: :environment do
    puts "Setting up test database..."

    # Store current environment
    current_env = Rails.env

    # Switch to test environment
    Rails.env = "test"

    # Create and migrate the database
    Rake::Task["db:drop"].invoke if ENV["FORCE_DROP"] == "true"
    Rake::Task["db:create"].invoke
    Rake::Task["db:schema:load"].invoke

    # Restore original environment
    Rails.env = current_env

    puts "Test database setup complete!"
  end

  desc "Setup staging database"
  task setup_staging: :environment do
    puts "Setting up staging database..."

    # Store current environment
    current_env = Rails.env

    # Switch to staging environment
    Rails.env = "staging"

    # Create, migrate, and seed the database
    Rake::Task["db:drop"].invoke if ENV["FORCE_DROP"] == "true"
    Rake::Task["db:create"].invoke
    Rake::Task["db:schema:load"].invoke
    Rake::Task["db:seed"].invoke

    # Restore original environment
    Rails.env = current_env

    puts "Staging database setup complete!"
  end

  desc "Setup production database"
  task setup_production: :environment do
    puts "Setting up production database..."

    # Store current environment
    current_env = Rails.env

    # Switch to production environment
    Rails.env = "production"

    # Create, migrate, and seed the database
    Rake::Task["db:drop"].invoke if ENV["FORCE_DROP"] == "true"
    Rake::Task["db:create"].invoke
    Rake::Task["db:schema:load"].invoke
    Rake::Task["db:seed"].invoke

    # Restore original environment
    Rails.env = current_env

    puts "Production database setup complete!"
  end
end
