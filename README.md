# SipTrails API

SipTrails is a Rails API application designed to manage and track beverage tasting trails. This application uses Grape for API development and provides comprehensive documentation through Swagger.

## Technology Stack

- **Ruby:** 3.4.4
- **Rails:** 8.0.2
- **Database:** PostgreSQL
- **API Framework:** Grape
- **Documentation:** Swagger
- **Testing:** RSpec
- **CORS:** rack-cors

## Setup and Installation

### Prerequisites

- Ruby 3.4.4
- PostgreSQL
- Bundler

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd sip_trails_ror
   ```

2. Install dependencies:
   ```bash
   bundle install
   ```

3. Configure the database:
   - Update `config/database.yml` with your PostgreSQL credentials if needed

4. Configure environment variables:
   - Copy the `.env.example` file to `.env`
   - Update the values in `.env` as needed
   - For CORS configuration, set `CORS_ALLOWED_ORIGINS` to a comma-separated list of allowed origins

5. Create and migrate the database:
   ```bash
   rails db:create db:migrate
   ```

6. Start the server:
   ```bash
   rails server
   ```

7. Access the API documentation at:
   ```
   http://localhost:3000/api/v1/swagger
   ```

## API Structure

The API is built using Grape and follows a versioned structure:

```
app/api/
├── api.rb                 # Base API configuration
├── v1/
│   ├── base.rb            # V1 API configuration
│   ├── entities/          # Response formatters
│   └── resources/         # API endpoints
```

### Current Endpoints

- `GET /api/v1/hello` - Returns a hello world message
- `GET /api/v1/hello/greet?name=YourName` - Returns a personalized greeting

## Testing

The application uses RSpec for testing. The test suite includes:

- API request specs
- Model specs (to be added)
- Integration tests (to be added)

### Running Tests

```bash
rspec
```

Or run specific test files:

```bash
rspec spec/api/v1/resources/hello_spec.rb
```

## Running CORS Specs

Due to Rails middleware limitations, CORS request specs (in `spec/requests/cors_spec.rb`) must be run in isolation. Running them as part of the full suite may cause failures because other specs can modify or reset the middleware stack.

**To run CORS specs:**

```
bundle exec rspec spec/requests/cors_spec.rb
```

Run the rest of your suite separately:

```
bundle exec rspec
```

This ensures reliable CORS test results.

## Development

### Adding New API Endpoints

1. Create a new resource file in `app/api/v1/resources/`
2. Define your endpoints using Grape DSL
3. Mount the resource in `app/api/v1/base.rb`
4. Add tests in `spec/api/v1/resources/`

### Response Entities

Use Grape Entity to format your API responses:

1. Create entity classes in `app/api/v1/entities/`
2. Use them in your API resources

### CORS Configuration

Cross-Origin Resource Sharing (CORS) is configured to allow requests from specified origins:

1. In development, CORS is configured to allow requests from:
   - http://localhost:3000
   - http://localhost:3001
   - http://localhost:8080

2. In production, set the `CORS_ALLOWED_ORIGINS` environment variable to a comma-separated list of allowed origins:
   ```
   CORS_ALLOWED_ORIGINS=https://yourappdomain.com,https://admin.yourappdomain.com
   ```

3. The CORS configuration exposes the following headers:
   - Authorization
   - X-Total-Count
   - X-Total-Pages

4. CORS is configured to allow all standard HTTP methods (GET, POST, PUT, PATCH, DELETE, OPTIONS, HEAD)

## Deployment

The application includes Docker and Kamal configuration for deployment:

1. Build the Docker image:
   ```bash
   docker build -t sip_trails_ror .
   ```

2. Deploy using Kamal:
   ```bash
   bin/kamal setup
   bin/kamal deploy
   ```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

[Add your license information here]
