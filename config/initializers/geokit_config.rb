require "geokit"
require "geokit-rails"

if defined?(GeoKit)
  # These defaults are used in GeoKit::Mappable.distance_to and in acts_as_mappable
  GeoKit.default_units = :miles
  GeoKit.default_formula = :sphere

  # This is the timeout value in seconds to be used for calls to the geocoder web
  # services.  For no timeout at all, comment out the setting.  The timeout unit
  # is in seconds.
  GeoKit::Geocoders.request_timeout = 3

  # This setting can be used if web service calls must be routed through a proxy.
  # These setting can be nil if not needed, otherwise, a valid URI must be
  # filled in at a minimum.  If the proxy requires authentication, the username
  # and password can be provided as well.
  # GeoKit::Geocoders::proxy = '**************************:port'

  # This is your yahoo application key for the Yahoo Geocoder.
  # See http://developer.yahoo.com/faq/index.html#appid
  # and http://developer.yahoo.com/maps/rest/V1/geocode.html
  # GeoKit::Geocoders::YahooGeocoder.key = 'REPLACE_WITH_YOUR_YAHOO_KEY'
  # GeoKit::Geocoders::YahooGeocoder.secret = 'REPLACE_WITH_YOUR_YAHOO_SECRET'

  # This is your Google Maps geocoder keys (all optional).
  # See http://www.google.com/apis/maps/signup.html
  # and http://www.google.com/apis/maps/documentation/#Geocoding_Examples
  # GeoKit::Geocoders::GoogleGeocoder.client_id = ''
  # GeoKit::Geocoders::GoogleGeocoder.cryptographic_key = ''
  # GeoKit::Geocoders::GoogleGeocoder.channel = ''

  # You can also use the free API key instead of signed requests
  # See https://developers.google.com/maps/documentation/geocoding/#api_key
  # GeoKit::Geocoders::GoogleGeocoder.api_key = ''

  # You can also set multiple API KEYS for different domains that may be directed
  # to this same application.
  # The domain from which the current user is being directed will automatically
  # be updated for Geokit via
  # the GeocoderControl class, which gets it's begin filter mixed
  # into the ActionController.
  # You define these keys with a Hash as follows:
  # GeoKit::Geocoders::google = {
  # 'rubyonrails.org' => 'RUBY_ON_RAILS_API_KEY',
  # ' ruby-docs.org' => 'RUBY_DOCS_API_KEY' }

  # This is your username and password for geocoder.us.
  # To use the free service, the value can be set to nil or false.  For
  # usage tied to an account, the value should be set to username:password.
  # See http://geocoder.us
  # and http://geocoder.us/user/signup
  # GeoKit::Geocoders::UsGeocoder.key = 'username:password'

  # This is your authorization key for geocoder.ca.
  # To use the free service, the value can be set to nil or false.  For
  # usage tied to an account, set the value to the key obtained from
  # Geocoder.ca.
  # See http://geocoder.ca
  # and http://geocoder.ca/?register=1
  # GeoKit::Geocoders::CaGeocoder.key = 'KEY'

  # This is your username key for geonames.
  # To use this service either free or premium, you must register a key.
  # See http://www.geonames.org
  # GeoKit::Geocoders::GeonamesGeocoder.key = 'KEY'

  # Most other geocoders need either no setup or a key
  # GeoKit::Geocoders::BingGeocoder.key = ''
  # GeoKit::Geocoders::MapQuestGeocoder.key = ''
  # GeoKit::Geocoders::YandexGeocoder.key = ''
  # GeoKit::Geocoders::MapboxGeocoder.key = 'ACCESS_TOKEN'
  # GeoKit::Geocoders::OpencageGeocoder.key = 'some_api_key'

  # Uncomment to use a test geocoder
  # GeoKit::Geocoders::TestGeocoder.use = true

  # The IP provider order. Valid symbols are :ip,:geo_plugin.
  # As before, make sure you read up on relevant Terms of Use for each.
  # GeoKit::Geocoders::ip_provider_order = [:external,:geo_plugin,:ip]

  # Disable HTTPS globally.  This option can also be set on individual
  # geocoder classes.
  # GeoKit::Geocoders::secure = false

  # Control verification of the server certificate for geocoders using HTTPS
  # GeoKit::Geocoders::ssl_verify_mode = OpenSSL::SSL::VERIFY_(PEER/NONE)
  # Setting this to VERIFY_NONE may be needed on systems that don't have
  # a complete or up to date root certificate store. Only applies to
  # the Net::HTTP adapter.

  # This is the order in which the geocoders are called in a failover scenario
  # If you only want to use a single geocoder, put a single symbol in the array.
  # Valid symbols are :google, :yahoo, :us, and :ca.
  # Be aware that there are Terms of Use restrictions on how you can use the
  # various geocoders.  Make sure you read up on relevant Terms of Use for each.
  # GeoKit::Geocoders::provider_order = [:google,:us]

  # The IP geocoder uses the timeout set in the Geocoders initializer,
  # and it's set to 3 seconds by default.
  # If you want to disable this initializer, uncomment the following line.
  # GeoKit::Geocoders::ip_provider_order = []

  # Provide a custom point class to use as the center of distance calculations
  # GeoKit::LatLng.default_point_class = MyCustomPointClass
end
