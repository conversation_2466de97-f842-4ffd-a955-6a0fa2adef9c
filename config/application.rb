require_relative "boot"

require "rails/all"

require "dotenv"
Dotenv.load

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module SipTrailsRor
  class Application < Rails::Application
    config.before_configuration do
      env_file = File.join(Rails.root, "config", "local_env.yml")
      next unless File.exist?(env_file)
       # load_file returns nil if the YAML is empty
       data = YAML.load_file(env_file) || {}

       data.each do |key, value|
         # only set if not already in ENV
         ENV[key.to_s] ||= value
       end
    end

    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true

    # tell Rails to eager-load your `app/api` folder
    config.paths.add Rails.root.join("app", "api").to_s, eager_load: true
    config.autoload_paths << Rails.root.join("app", "api")

    # if you want it eager‐loaded in production as well:
    # Enable serving of static files
    config.public_file_server.enabled = true
  end
end
