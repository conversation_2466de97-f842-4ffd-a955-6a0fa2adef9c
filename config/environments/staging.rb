require_relative "production"

Rails.application.configure do
  # Override production settings here

  # For example, you might want different log levels
  config.log_level = :debug

  # Or different mailer settings
  config.action_mailer.default_url_options = { host: "staging.siptrails.com" }

  # setup web socket for staging
  config.action_cable.allowed_request_origins = [ "https://staging.siptrails.com", "http://localhost:3000" ]
  config.action_cable.url = "wss://staging.siptrails.com/cable"
end
