require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded any time
  # it changes. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.enable_reloading = true

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  config.hosts.clear

  # setup web socket
  config.action_cable.allowed_request_origins = [ "https://sip-trails-ror-or8dl.ondigitalocean.app", "http://localhost:3000" ]
  config.action_cable.url = "ws://localhost:3000/cable"
  config.action_cable.disable_request_forgery_protection = true

  # Enable server timing
  config.server_timing = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    config.cache_store = :memory_store
    config.public_file_server.headers = {
      "Cache-Control" => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false
    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.perform_caching = false

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Enable serving of static files
  config.public_file_server.enabled = true
  config.public_file_server.headers = {
    "Cache-Control" => "public, max-age=#{1.hour.to_i}"
  }

  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  config.file_watcher = ActiveSupport::EventedFileUpdateChecker

  config.active_job.queue_name_prefix = "siptrails_#{Rails.env}"
  config.active_job.queue_adapter = :async

  # logger = Logger.new('/proc/1/fd/1')
  # logger.formatter = config.log_formatter
  # config.logger = ActiveSupport::TaggedLogging.new(logger)

  #   config.action_mailer.delivery_method = :smtp
  # host = 'sip-trails-ror-gbn2g.ondigitalocean.app' #replace with your own url
  # config.action_mailer.default_url_options = { host: host }
  # config.action_mailer.perform_deliveries = true
  # config.action_mailer.raise_delivery_errors = true
  #
  # # SMTP settings for gmail
  # config.action_mailer.smtp_settings = {
  #   :address              => 'smtp.gmail.com',
  #   :port                 => 587,
  #   :domain               => 'sip-trails-ror-gbn2g.ondigitalocean.app',
  #   :user_name            => ENV['SMTP_EMAIL'],
  #   :password             => ENV['SMTP_PAS'],
  #   :authentication       => 'plain',
  #   :enable_starttls_auto => true
  # }
end
