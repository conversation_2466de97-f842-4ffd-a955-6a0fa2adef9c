# require 'rails_helper'
# require Rails.root.join('app/api/api.rb')

# RSpec.describe 'Swagger Documentation', type: :request, api_http: true do
#   describe 'GET /api/v1/swagger_doc' do
#     before do
#       get '/api/v1/swagger_doc'
#     end

#     it 'returns a successful response' do
#       expect(response).to be_successful
#     end

#     it 'returns valid JSON' do
#       expect { JSON.parse(response.body) }.not_to raise_error
#     end

#     it 'includes API information' do
#       swagger_doc = JSON.parse(response.body)
#       expect(swagger_doc).to include('info')
#       expect(swagger_doc).to include('paths')
#     end
#   end
# end
