require 'rails_helper'

RSpec.describe 'V1 Settings Vendor API', type: :request, api_http: true do
  let(:vendor_user) { create(:user, :vendor, verified: true) }
  let(:admin_user) { create(:user, :admin, verified: true) }
  let(:regular_user) { create(:user, verified: true) }
  let(:unverified_vendor) { create(:user, :vendor, verified: false) }

  let(:vendor_token) { vendor_user.generate_jwt }
  let(:admin_token) { admin_user.generate_jwt }
  let(:regular_token) { regular_user.generate_jwt }
  let(:unverified_token) { unverified_vendor.generate_jwt }

  let(:vendor_headers) { json_request_headers.merge('Authorization' => "Bearer #{vendor_token}") }
  let(:admin_headers) { json_request_headers.merge('Authorization' => "Bearer #{admin_token}") }
  let(:regular_headers) { json_request_headers.merge('Authorization' => "Bearer #{regular_token}") }
  let(:unverified_headers) { json_request_headers.merge('Authorization' => "Bearer #{unverified_token}") }

  describe 'POST /api/v1/settings_vendor/accept_invite' do
    let(:team) { create(:team) }
    let(:facility) { create(:facility, team: team) }

    context 'when authenticated as vendor' do
      context 'with valid team_id' do
        it 'accepts team invitation and updates user team' do
          post '/api/v1/settings_vendor/accept_invite',
               params: { team_id: team.id }.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(201)
          vendor_user.reload
          expect(vendor_user.team).to eq(team)

          body = json_response
          expect(body[:id]).to eq(vendor_user.id)
          expect(body[:team][:id]).to eq(team.id)
        end

        it 'returns user entity with team information' do
          post '/api/v1/settings_vendor/accept_invite',
               params: { team_id: team.id }.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(201)
          body = json_response

          expect(body).to have_key(:id)
          expect(body).to have_key(:role)
          expect(body).to have_key(:verified)
          expect(body).to have_key(:team)
          expect(body[:team][:id]).to eq(team.id)
          expect(body[:team][:name]).to eq(team.name)
        end
      end

      context 'with non-existent team_id' do
        it 'returns 401 with TeamErrors_NotFound code' do
          post '/api/v1/settings_vendor/accept_invite',
               params: { team_id: 'non-existent' }.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(401)
          body = json_response
          expect(body[:message]).to eq('Team not found')
          expect(body[:code]).to eq(Constants::TeamErrors::TeamErrors_NotFound)
        end
      end

      context 'when user save fails' do
        let(:failing_vendor) { create(:user, :vendor, verified: true) }
        let(:failing_headers) { json_request_headers.merge('Authorization' => "Bearer #{failing_vendor.generate_jwt}") }

        before do
          allow_any_instance_of(User).to receive(:save!).and_return(false)
        end

        it 'returns 401 with SaveErrors_FailedToSaveInfo code' do
          post '/api/v1/settings_vendor/accept_invite',
               params: { team_id: team.id }.to_json,
               headers: failing_headers

          expect(response).to have_http_status(401)
          body = json_response
          expect(body[:message]).to eq('401 Unauthorized token - unable to find headers')
          expect(body[:code]).to eq(401)
        end
      end

      context 'without team_id parameter' do
        it 'returns 400 for missing required parameter' do
          post '/api/v1/settings_vendor/accept_invite',
               params: {}.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(400)
        end
      end
    end

    context 'when authenticated as admin' do
      it 'allows admin to accept team invitation' do
        post '/api/v1/settings_vendor/accept_invite',
             params: { team_id: team.id }.to_json,
             headers: admin_headers

        expect(response).to have_http_status(201)
        admin_user.reload
        expect(admin_user.team).to eq(team)
      end
    end

    context 'when authenticated as regular user' do
      it 'returns 403 for non-vendor user' do
        post '/api/v1/settings_vendor/accept_invite',
             params: { team_id: team.id }.to_json,
             headers: regular_headers

        expect(response).to have_http_status(403)
        body = json_response
        expect(body[:message]).to include('403 Unauthorized only vendors can access')
      end
    end

    context 'when not authenticated' do
      it 'returns 401 without authorization header' do
        post '/api/v1/settings_vendor/accept_invite',
             params: { team_id: team.id }.to_json,
             headers: json_request_headers

        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to include('401 Unauthorized')
      end

      it 'returns 401 with invalid token' do
        invalid_headers = json_request_headers.merge('Authorization' => 'Bearer invalid_token')
        post '/api/v1/settings_vendor/accept_invite',
             params: { team_id: team.id }.to_json,
             headers: invalid_headers

        expect(response).to have_http_status(401)
      end
    end
  end

  describe 'POST /api/v1/settings_vendor' do
    let(:team) { create(:team) }
    let(:facility) { create(:facility, team: team) }

    before do
      puts "##################### updating user team "
      team.update!(facility: facility)
      vendor_user.update!(team: team)
    end

    context 'when authenticated as vendor with facility' do
      context 'with valid facility update parameters' do
        let(:update_params) do
          {
            name: 'Updated Facility Name',
            handle: 'updated_handle',
            phone: '+1234567890',
            street: '123 Updated St',
            city: 'Updated City',
            state: 'CA',
            zipcode: '90210',
            website: 'https://updated.example.com',
            facebook: 'updated_facebook',
            instagram: 'updated_instagram',
            twitter: 'updated_twitter',
            latitude: '34.0522',
            longitude: '-118.2437',
            radius: '5.0'
          }
        end

        it 'updates facility with provided parameters' do
          post '/api/v1/settings_vendor',
               params: update_params.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(201)
          facility.reload

          expect(facility.name).to eq('Updated Facility Name')
          expect(facility.handle).to eq('updated_handle')
          expect(facility.phone).to eq('+1234567890')
          expect(facility.street).to eq('123 Updated St')
          expect(facility.city).to eq('Updated City')
          expect(facility.state).to eq('CA')
          expect(facility.zipcode).to eq('90210')
          expect(facility.website).to eq('https://updated.example.com')
          expect(facility.facebook).to eq('updated_facebook')
          expect(facility.instagram).to eq('updated_instagram')
          expect(facility.twitter).to eq('updated_twitter')
          expect(facility.latitude.to_s).to eq('34.0522')
          expect(facility.longitude.to_s).to eq('-118.2437')
          expect(facility.radius.to_s).to eq('5.0')
        end

        it 'returns updated facility data' do
          post '/api/v1/settings_vendor',
               params: update_params.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(201)
          body = json_response

          expect(body[:name]).to eq('Updated Facility Name')
          expect(body[:handle]).to eq('updated_handle')
          # Note: phone is not exposed in the Facility entity, so we won't check it in the response
        end

        it 'updates only provided parameters' do
          partial_params = { name: 'Partial Update', city: 'New City' }
          original_handle = facility.handle

          post '/api/v1/settings_vendor',
               params: partial_params.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(201)
          facility.reload

          expect(facility.name).to eq('Partial Update')
          expect(facility.city).to eq('New City')
          expect(facility.handle).to eq(original_handle) # Should remain unchanged
        end

        it 'handles empty parameters gracefully' do
          post '/api/v1/settings_vendor',
               params: {}.to_json,
               headers: vendor_headers

          body = json_response
          puts body
          expect(response).to have_http_status(201)
        end
      end

      context 'with file uploads' do
        let(:avatar_file) do
          fixture_file_upload(Rails.root.join('spec', 'fixtures', 'files', 'test_avatar.jpg'), 'image/jpeg')
        end

        let(:cover_file) do
          fixture_file_upload(Rails.root.join('spec', 'fixtures', 'files', 'test_cover.jpg'), 'image/jpeg')
        end

        it 'updates facility avatar' do
          post '/api/v1/settings_vendor',
               params: { avatar: avatar_file, name: 'With Avatar' },
               headers: { 'Authorization' => "Bearer #{vendor_token}" }

          expect(response).to have_http_status(200)
          facility.reload
          expect(facility.avatar).to be_present
          expect(facility.name).to eq('With Avatar')
        end

        it 'updates facility cover image' do
          post '/api/v1/settings_vendor',
               params: { cover: cover_file, name: 'With Cover' },
               headers: { 'Authorization' => "Bearer #{vendor_token}" }

          expect(response).to have_http_status(200)
          facility.reload
          expect(facility.cover).to be_present
          expect(facility.name).to eq('With Cover')
        end

        it 'updates both avatar and cover' do
          post '/api/v1/settings_vendor',
               params: { avatar: avatar_file, cover: cover_file, name: 'With Both' },
               headers: { 'Authorization' => "Bearer #{vendor_token}" }

          expect(response).to have_http_status(200)
          facility.reload
          expect(facility.avatar).to be_present
          expect(facility.cover).to be_present
          expect(facility.name).to eq('With Both')
        end
      end

      context 'when facility update fails' do
        before do
          allow_any_instance_of(Facility).to receive(:update).and_return(false)
          allow_any_instance_of(Facility).to receive_message_chain(:errors, :objects, :first, :full_message)
            .and_return('Validation failed')
        end

        it 'returns 404 with SaveErrors_FailedToSaveInfo code' do
          post '/api/v1/settings_vendor',
               params: { name: 'Should Fail' }.to_json,
               headers: vendor_headers

          expect(response).to have_http_status(404)
          body = json_response
          expect(body[:message]).to eq('Validation failed')
          expect(body[:code]).to eq(Constants::SaveErrors::SaveErrors_FailedToSaveInfo)
        end
      end
    end

    context 'when vendor has no facility' do
      before do
        vendor_user.update!(team: nil)
      end

      it 'returns 401 when user has no team facility' do
        post '/api/v1/settings_vendor',
             params: { name: 'Should Fail' }.to_json,
             headers: vendor_headers

        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to eq('Failed to get facility')
        expect(body[:code]).to eq(Constants::SaveErrors::SaveErrors_FailedToSaveInfo)
      end
    end

    context 'when authenticated as admin with facility' do
      before do
        admin_user.update!(team: team)
      end

      it 'allows admin to update facility settings' do
        post '/api/v1/settings_vendor',
             params: { name: 'Admin Updated' }.to_json,
             headers: admin_headers

        expect(response).to have_http_status(200)
        facility.reload
        expect(facility.name).to eq('Admin Updated')
      end
    end

    context 'when authenticated as regular user' do
      it 'returns 403 for non-vendor user' do
        post '/api/v1/settings_vendor',
             params: { name: 'Should Fail' }.to_json,
             headers: regular_headers

        expect(response).to have_http_status(403)
        body = json_response
        expect(body[:message]).to include('403 Unauthorized only vendors can access')
      end
    end

    context 'when not authenticated' do
      it 'returns 401 without authorization header' do
        post '/api/v1/settings_vendor',
             params: { name: 'Should Fail' }.to_json,
             headers: json_request_headers

        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to include('401 Unauthorized')
      end

      it 'returns 401 with invalid token' do
        invalid_headers = json_request_headers.merge('Authorization' => 'Bearer invalid_token')
        post '/api/v1/settings_vendor',
             params: { name: 'Should Fail' }.to_json,
             headers: invalid_headers

        expect(response).to have_http_status(401)
      end

      it 'returns 401 with expired token' do
        expired_payload = { id: vendor_user.id, exp: 1.hour.ago.to_i }
        expired_token = JWT.encode(expired_payload, Rails.application.secret_key_base)
        expired_headers = json_request_headers.merge('Authorization' => "Bearer #{expired_token}")

        post '/api/v1/settings_vendor',
             params: { name: 'Should Fail' }.to_json,
             headers: expired_headers

        expect(response).to have_http_status(401)
      end
    end

    context 'parameter validation' do
      it 'ignores unknown parameters' do
        params_with_unknown = {
          name: 'Valid Name',
          unknown_param: 'should_be_ignored',
          another_unknown: 'also_ignored'
        }

        post '/api/v1/settings_vendor',
             params: params_with_unknown.to_json,
             headers: vendor_headers

        expect(response).to have_http_status(200)
        facility.reload
        expect(facility.name).to eq('Valid Name')
      end

      it 'handles nil values gracefully' do
        post '/api/v1/settings_vendor',
             params: { name: nil, phone: nil }.to_json,
             headers: vendor_headers

        expect(response).to have_http_status(200)
        facility.reload
        expect(facility.name).to be_nil
        expect(facility.phone).to be_nil
      end
    end

    context 'response format' do
      it 'returns facility entity with proper structure' do
        post '/api/v1/settings_vendor',
             params: { name: 'Response Test' }.to_json,
             headers: vendor_headers

        expect(response).to have_http_status(200)
        body = json_response

        # Check that response includes expected facility entity fields
        expect(body).to have_key(:id)
        expect(body).to have_key(:name)
        expect(body).to have_key(:handle)
        expect(body).to have_key(:latitude)
        expect(body).to have_key(:longitude)
        expect(body).to have_key(:avatar)
        expect(body).to have_key(:cover)
        expect(body).to have_key(:is_following)
        expect(body).to have_key(:followers)
        expect(body[:name]).to eq('Response Test')
      end
    end
  end

  # Test both endpoints with unverified vendor
  context 'when vendor is not verified' do
    it 'accept_invite endpoint still works (no verified! check)' do
      team = create(:team)
      post '/api/v1/settings_vendor/accept_invite',
           params: { team_id: team.id }.to_json,
           headers: unverified_headers

      expect(response).to have_http_status(200)
    end

    it 'settings update endpoint still works (no verified! check)' do
      team = create(:team)
      facility = create(:facility, team: team)
      unverified_vendor.update!(team: team)

      post '/api/v1/settings_vendor',
           params: { name: 'Unverified Update' }.to_json,
           headers: unverified_headers

      expect(response).to have_http_status(200)
    end
  end
end
