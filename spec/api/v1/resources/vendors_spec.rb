require 'rails_helper'

RSpec.describe 'V1 Vendors API', type: :request, api_http: true do
  let(:facility) { create(:facility) }
  let(:team) { Team.create!(name: facility.name) }

  let(:vendor_user) do
    create(:user, role: 1, verified: true).tap do |u|
      # ensure user has JWT
      @vendor_token = u.generate_jwt
    end
  end

  let(:headers) do
    {
      'Authorization' => "Bearer #{@vendor_token}"
    }
  end

  describe 'PUT /api/v1/vendors/set_owner' do
    context 'when unauthorized' do
      it 'returns 401 without token' do
        put '/api/v1/vendors/set_owner', params: { facility_id: facility.id }.to_json, headers: json_request_headers
        expect(response).to have_http_status(401)
      end
    end

    context 'when user is not a vendor' do
      it 'returns 403 for non-vendor user' do
        non_vendor = create(:user, role: 0, verified: true)
        token = non_vendor.generate_jwt
        put '/api/v1/vendors/set_owner',
            params: { facility_id: facility.id }.to_json,
            headers: json_request_headers.merge('Authorization' => "Bearer #{token}")
        expect(response).to have_http_status(403)
      end
    end

    context 'when facility does not exist' do
      it 'returns 401 with FacilityErrors_NotFound code' do
        vendor_user
        put '/api/v1/vendors/set_owner',
            params: { facility_id: 'non-existent' }.to_json,
            headers: json_request_headers.merge(headers)
        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to eq('Facility not found')
        expect(body[:code]).to eq(Constants::FacilityErrors::FacilityErrors_NotFound)
      end
    end

    context 'when facility already has an owner' do
      it 'returns 401 with VendorErrors_FacilityAlreadyHasOwner code' do
        vendor_user
        existing_owner = create(:user, role: 1, verified: true)
        facility.update!(user: existing_owner)

        put '/api/v1/vendors/set_owner',
            params: { facility_id: facility.id }.to_json,
            headers: json_request_headers.merge(headers)

        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to eq('Facility already has owner')
        expect(body[:code]).to eq(Constants::VendorErrors::VendorErrors_FacilityAlreadyHasOwner)
      end
    end

    context 'when successful' do
      it 'assigns facility to current user and creates/assigns team' do
        vendor = vendor_user

        put '/api/v1/vendors/set_owner',
            params: { facility_id: facility.id }.to_json,
            headers: json_request_headers.merge(headers)

        expect(response).to have_http_status(200)
        vendor.reload
        facility.reload

        expect(vendor.facility).to eq(facility)
        expect(vendor.team).not_to be_nil
        expect(vendor.team.name).to eq(facility.name)
      end
    end
  end
end
