require 'rails_helper'

RSpec.describe 'V1 Perks API', type: :request, api_http: true do

  let(:user) { create(:user, verified: true) }
  let(:vendor) { create(:user, :vendor, verified: true) }
  let(:admin) { create(:user, :admin, verified: true) }
  
  let(:token) { user.generate_jwt }
  let(:vendor_token) { vendor.generate_jwt }
  let(:admin_token) { admin.generate_jwt }
  
  let(:headers) { json_request_headers.merge('Authorization' => "Bearer #{token}") }
  let(:vendor_headers) { json_request_headers.merge('Authorization' => "Bearer #{vendor_token}") }
  let(:admin_headers) { json_request_headers.merge('Authorization' => "Bearer #{admin_token}") }

  let!(:facility) { create(:facility) }
  let!(:team) { create(:team, name: facility.name) }
  let!(:perk) { create(:perk, facility: facility) }
  let!(:user_perk) { create(:user_perk, perk: perk, user: user) }

  before do
    facility.update!(team: team)
    vendor.update!(team: team)
    admin.update!(team: team)
    # Ensure the facility belongs to the team
    facility.reload
    # Ensure users have access to the facility through their team
    vendor.reload
    admin.reload
  end

  describe 'DELETE /api/v1/perks' do
    it 'requires vendor role' do
      delete '/api/v1/perks', params: { perk: perk.id }.to_json, headers: headers
      expect(response).to have_http_status(403)
    end

    it 'deletes a perk successfully for vendor' do
      delete '/api/v1/perks', params: { perk: perk.id }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      expect(Perk.find_by(id: perk.id)).to be_nil
    end

    it 'deletes a perk successfully for admin' do
      delete '/api/v1/perks', params: { perk: perk.id }.to_json, headers: admin_headers
      expect(response).to have_http_status(200)
      expect(Perk.find_by(id: perk.id)).to be_nil
    end

    it 'returns 403 when perk not found' do
      delete '/api/v1/perks', params: { perk: 'missing-id' }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_NotFound)
      expect(body[:message]).to eq('Failed to find a perk')
    end

    it 'deletes associated user_perks when perk is deleted' do
      expect(UserPerk.where(perk_id: perk.id)).to exist
      delete '/api/v1/perks', params: { perk: perk.id }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      expect(UserPerk.where(perk_id: perk.id)).not_to exist
    end
  end

  describe 'PUT /api/v1/perks/claim' do
    let(:checkin_token) do
      token_data = { id: user_perk.id, code: user_perk.code }.to_json
      Base64.strict_encode64(token_data)
    end

    it 'requires vendor role' do
      put '/api/v1/perks/claim', params: { token: checkin_token }.to_json, headers: headers
      expect(response).to have_http_status(403)
    end

    it 'claims a perk successfully for vendor' do
      put '/api/v1/perks/claim', params: { token: checkin_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      user_perk.reload
      expect(user_perk.is_claimed).to be true
    end

    it 'claims a perk successfully for admin' do
      put '/api/v1/perks/claim', params: { token: checkin_token }.to_json, headers: admin_headers
      expect(response).to have_http_status(200)
      user_perk.reload
      expect(user_perk.is_claimed).to be true
    end

    it 'returns 403 when perk not found' do
      invalid_token = Base64.strict_encode64({ id: 'missing-id', code: 123456 }.to_json)
      put '/api/v1/perks/claim', params: { token: invalid_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_NotFound)
      expect(body[:message]).to eq('Failed to find a perk')
    end

    it 'returns 403 when perk is already claimed' do
      user_perk.update!(is_claimed: true)
      put '/api/v1/perks/claim', params: { token: checkin_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_AlreadyClaimed)
      expect(body[:message]).to eq('This perk is already claimed')
    end

    it 'returns 403 when vendor does not own the perk' do
      other_facility = create(:facility)
      other_team = create(:team, name: other_facility.name)
      other_facility.update!(team: other_team)
      other_perk = create(:perk, facility: other_facility)
      other_user_perk = create(:user_perk, perk: other_perk, user: user)
      
      other_token = Base64.strict_encode64({ id: other_user_perk.id, code: other_user_perk.code }.to_json)
      put '/api/v1/perks/claim', params: { token: other_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_NotOwned)
      expect(body[:message]).to eq('Your team dose not own this perk')
    end

    it 'returns 403 when code is invalid' do
      invalid_token = Base64.strict_encode64({ id: user_perk.id, code: 999999 }.to_json)
      put '/api/v1/perks/claim', params: { token: invalid_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_InvalidCode)
      expect(body[:message]).to eq('invalid code for token and perk')
    end

    it 'enqueues NotifyOfPerkClaimJob when claiming' do
      expect(NotifyOfPerkClaimJob).to receive(:perform_later).with(user_perk.id)
      put '/api/v1/perks/claim', params: { token: checkin_token }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET /api/v1/perks/all' do
    it 'returns all perks for a facility with pagination' do
      create_list(:perk, 15, facility: facility)
      
      get '/api/v1/perks/all', params: { id: facility.id, per_page: 10, page: 1 }, headers: headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body).to be_an(Array)
      expect(body.length).to eq(10)
    end

    it 'returns 403 when facility not found' do
      get '/api/v1/perks/all', params: { id: 'missing-id' }, headers: headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::FacilityErrors::FacilityErrors_NotFound)
      expect(body[:message]).to eq('Failed to find a facility')
    end

    it 'uses default pagination when not specified' do
      get '/api/v1/perks/all', params: { id: facility.id }, headers: headers
      expect(response).to have_http_status(200)
    end

    it 'orders perks by updated_at desc' do
      old_perk = create(:perk, facility: facility, updated_at: 2.days.ago)
      new_perk = create(:perk, facility: facility, updated_at: DateTime.now)
      
      get '/api/v1/perks/all', params: { id: facility.id }, headers: headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body.first[:id]).to eq(new_perk.id)
      expect(body.last[:id]).to eq(old_perk.id)
    end
  end

  describe 'GET /api/v1/perks/mine' do
    it 'returns current user perks with pagination' do
      create_list(:user_perk, 15, user: user)
      
      get '/api/v1/perks/mine', params: { per_page: 10, page: 1 }, headers: headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body).to be_an(Array)
      expect(body.length).to eq(10)
    end

    it 'uses default pagination when not specified' do
      get '/api/v1/perks/mine', headers: headers
      expect(response).to have_http_status(200)
    end

    it 'orders user perks by updated_at desc' do
      old_user_perk = create(:user_perk, user: user, updated_at: 2.days.ago)
      new_user_perk = create(:user_perk, user: user, updated_at: DateTime.now)
      
      get '/api/v1/perks/mine', headers: headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body.first[:id]).to eq(new_user_perk.id)
      expect(body.last[:id]).to eq(old_user_perk.id)
    end
  end

  describe 'POST /api/v1/perks/create' do
    let(:valid_params) do
      {
        startdate: 1.day.from_now,
        enddate: 30.days.from_now,
        checkins_required: 5,
        title: 'New Perk',
        reward: 'Free drink'
      }
    end

    it 'requires vendor role' do
      post '/api/v1/perks/create', params: valid_params.to_json, headers: headers
      expect(response).to have_http_status(403)
    end

    it 'creates a new perk successfully for vendor' do
      expect {
        post '/api/v1/perks/create', params: valid_params.to_json, headers: vendor_headers
      }.to change(Perk, :count).by(1)
      
      expect(response).to have_http_status(201)
      body = json_response
      expect(body[:title]).to eq('New Perk')
      expect(body[:facility_id]).to eq(facility.id)
    end

    it 'creates a new perk successfully for admin' do
      expect {
        post '/api/v1/perks/create', params: valid_params.to_json, headers: admin_headers
      }.to change(Perk, :count).by(1)
      expect(response).to have_http_status(201)
    end

    it 'enqueues NotifyOfNewPerkJob when creating' do
      expect(NotifyOfNewPerkJob).to receive(:perform_later).with(facility.id, anything)
      post '/api/v1/perks/create', params: valid_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)
    end

    it 'returns 403 when perk creation fails' do
      invalid_params = valid_params.merge(startdate: nil)
      post '/api/v1/perks/create', params: invalid_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(400)
      body = json_response
      expect(body[:error]).to eq('startdate is empty')
    end

    it 'handles avatar upload' do
      # This test would need to be adjusted based on how file uploads are handled in tests
      # For now, we'll test without avatar
      post '/api/v1/perks/create', params: valid_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)
    end
  end

  describe 'PATCH /api/v1/perks/edit' do
    let(:update_params) do
      {
        id: perk.id,
        title: 'Updated Perk Title',
        reward: 'Updated Reward'
      }
    end

    it 'requires vendor role' do
      patch '/api/v1/perks/edit', params: update_params.to_json, headers: headers
      expect(response).to have_http_status(403)
    end

    it 'updates a perk successfully for vendor' do
      patch '/api/v1/perks/edit', params: update_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      
      perk.reload
      expect(perk.title).to eq('Updated Perk Title')
      expect(perk.reward).to eq('Updated Reward')
    end

    it 'updates a perk successfully for admin' do
      patch '/api/v1/perks/edit', params: update_params.to_json, headers: admin_headers
      expect(response).to have_http_status(200)
    end

    it 'returns 403 when perk not found' do
      patch '/api/v1/perks/edit', params: { id: 'missing-id', title: 'Updated' }.to_json, headers: vendor_headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::PerkErrors::PerkErrors_NotFound)
      expect(body[:message]).to eq('Failed to find perk for id')
    end

    it 'updates only specified fields' do
      original_title = perk.title
      original_reward = perk.reward
      
      patch '/api/v1/perks/edit', params: { id: perk.id, title: 'New Title' }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      
      perk.reload
      expect(perk.title).to eq('New Title')
      expect(perk.reward).to eq(original_reward)
    end

    it 'handles partial updates' do
      patch '/api/v1/perks/edit', params: { id: perk.id, checkins_required: 10 }.to_json, headers: vendor_headers
      expect(response).to have_http_status(200)
      
      perk.reload
      expect(perk.checkins_required).to eq(10)
    end
  end

  describe 'authentication and authorization' do
    it 'requires authentication for all endpoints' do
      get '/api/v1/perks/mine', headers: json_request_headers
      expect(response).to have_http_status(401)
    end

    it 'requires user verification for all endpoints' do
      unverified_user = create(:user, role: 0, verified: false)
      unverified_token = unverified_user.generate_jwt
      unverified_headers = json_request_headers.merge('Authorization' => "Bearer #{unverified_token}")
      
      get '/api/v1/perks/mine', headers: unverified_headers
      expect(response).to have_http_status(403)
    end
  end
end
