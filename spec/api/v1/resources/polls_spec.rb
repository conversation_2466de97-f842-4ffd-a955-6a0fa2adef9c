require 'rails_helper'

RSpec.describe 'V1 Polls API', type: :request, api_http: true do
  let(:user) { create(:user, verified: true) }
  let(:vendor) { create(:user, :vendor, verified: true) }
  let(:admin) { create(:user, :admin, verified: true) }
  let(:unverified_user) { create(:user, :unverified) }

  let(:token) { user.generate_jwt }
  let(:vendor_token) { vendor.generate_jwt }
  let(:admin_token) { admin.generate_jwt }
  let(:unverified_token) { unverified_user.generate_jwt }

  let(:headers) { json_request_headers.merge('Authorization' => "Bearer #{token}") }
  let(:vendor_headers) { json_request_headers.merge('Authorization' => "Bearer #{vendor_token}") }
  let(:admin_headers) { json_request_headers.merge('Authorization' => "Bearer #{admin_token}") }
  let(:unverified_headers) { json_request_headers.merge('Authorization' => "Bearer #{unverified_token}") }

  let(:facility) { create(:facility) }
  let(:team) { create(:team) }

  before do
    # Set up vendor with team and facility
    vendor.update!(team: team)
    facility.update!(team: team)
  end

  describe 'Authentication and Authorization' do
    it 'requires authentication for all endpoints' do
      get '/api/v1/polls/poll'
      expect(response).to have_http_status(401)
      expect(json_response[:message]).to include('401 Unauthorized')
    end

    it 'requires verification for all endpoints' do
      get '/api/v1/polls/poll', headers: unverified_headers
      expect(response).to have_http_status(403)
      expect(json_response[:message]).to include('user is not verified')
    end
  end

  describe 'GET /api/v1/polls/poll' do
    context 'when no facilityId is provided' do
      let!(:global_poll1) { create(:poll, :global, :with_options, question: 'Global Poll 1') }
      let!(:global_poll2) { create(:poll, :global, :with_options, question: 'Global Poll 2') }
      let!(:facility_poll) { create(:poll, facility: facility, question: 'Facility Poll') }

      it 'returns the most recent unanswered global poll' do
        get '/api/v1/polls/poll', headers: headers
        expect(response).to have_http_status(200)

        body = json_response
        expect(body[:question]).to eq('Global Poll 2')
        expect(body[:facility_id]).to be_nil
      end

      it 'excludes polls the user has already answered' do
        # User answers the most recent poll
        option = global_poll2.options.first
        create(:user_response, user: user, poll: global_poll2, option: option)

        get '/api/v1/polls/poll', headers: headers
        expect(response).to have_http_status(200)

        body = json_response
        expect(body[:question]).to eq('Global Poll 1')
      end

      it 'returns nil when no unanswered polls exist' do
        # User answers all global polls
        [global_poll1, global_poll2].each do |poll|
          option = poll.options.first
          create(:user_response, user: user, poll: poll, option: option)
        end

        get '/api/v1/polls/poll', headers: headers
        expect(response).to have_http_status(200)
        expect(response.body.strip).to eq('null')
      end
    end

    context 'when facilityId is provided' do
      let!(:facility_poll1) { create(:poll, facility: facility, question: 'Facility Poll 1') }
      let!(:facility_poll2) { create(:poll, facility: facility, question: 'Facility Poll 2') }
      let!(:other_facility_poll) { create(:poll, facility: create(:facility), question: 'Other Facility Poll') }
      let!(:global_poll) { create(:poll, :global, question: 'Global Poll') }

      before do
        # Add options to polls
        [facility_poll1, facility_poll2, other_facility_poll, global_poll].each do |poll|
          create_list(:option, 2, poll: poll)
        end
      end

      it 'returns unanswered polls for the specified facility' do
        get '/api/v1/polls/poll', params: { facilityId: facility.id }, headers: headers
        expect(response).to have_http_status(200)

        body = json_response
        expect([facility_poll1.question, facility_poll2.question]).to include(body[:question])
        expect(body[:facility_id]).to eq(facility.id)
      end

      it 'excludes answered polls for the facility' do
        option = facility_poll1.options.first
        create(:user_response, user: user, poll: facility_poll1, option: option)

        get '/api/v1/polls/poll', params: { facilityId: facility.id }, headers: headers
        expect(response).to have_http_status(200)

        body = json_response
        expect(body[:question]).to eq(facility_poll2.question)
      end

      it 'does not return polls from other facilities' do
        # Answer facility polls
        [facility_poll1, facility_poll2].each do |poll|
          option = poll.options.first
          create(:user_response, user: user, poll: poll, option: option)
        end

        get '/api/v1/polls/poll', params: { facilityId: facility.id }, headers: headers
        expect(response).to have_http_status(200)
        expect(response.body.strip).to eq('null')
      end
    end
  end

  describe 'POST /api/v1/polls/create_poll' do
    let(:poll_params) do
      {
        question: 'What is your favorite wine type?',
        options: [
          { option: 'Red Wine' },
          { option: 'White Wine' },
          { option: 'Rosé Wine' }
        ]
      }
    end

    it 'requires vendor role' do
      post '/api/v1/polls/create_poll', params: poll_params.to_json, headers: headers
      expect(response).to have_http_status(403)
      expect(json_response[:message]).to include('only vendors can access')
    end

    it 'allows admin to create polls' do
      admin.update!(team: team)

      post '/api/v1/polls/create_poll', params: poll_params.to_json, headers: admin_headers
      expect(response).to have_http_status(201)

      body = json_response
      expect(body[:question]).to eq('What is your favorite wine type?')
      expect(body[:facility_id]).to eq(facility.id)
      expect(body[:options]).to be_an(Array)
      expect(body[:options].length).to eq(3)
    end

    it 'creates a poll with options for vendor' do
      post '/api/v1/polls/create_poll', params: poll_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)

      body = json_response
      expect(body[:question]).to eq('What is your favorite wine type?')
      expect(body[:facility_id]).to eq(facility.id)
      expect(body[:options]).to be_an(Array)
      expect(body[:options].length).to eq(3)

      option_texts = body[:options].map { |opt| opt[:option] }
      expect(option_texts).to include('Red Wine', 'White Wine', 'Rosé Wine')
    end

    it 'creates poll and options in database' do
      expect {
        post '/api/v1/polls/create_poll', params: poll_params.to_json, headers: vendor_headers
      }.to change(Poll, :count).by(1).and change(Option, :count).by(3)

      poll = Poll.last
      expect(poll.question).to eq('What is your favorite wine type?')
      expect(poll.facility_id).to eq(facility.id)
      expect(poll.options.count).to eq(3)
    end

    it 'validates required parameters' do
      post '/api/v1/polls/create_poll', params: { question: 'Test?' }.to_json, headers: vendor_headers
      expect(response).to have_http_status(400)
    end

    it 'validates question presence' do
      invalid_params = poll_params.merge(question: '')
      post '/api/v1/polls/create_poll', params: invalid_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(422)
    end
  end

  describe 'DELETE /api/v1/polls' do
    let!(:global_poll) { create(:poll, :global, :with_options) }
    let!(:facility_poll) { create(:poll, :with_options, facility: facility) }
    let!(:other_facility_poll) { create(:poll, :with_options, facility: create(:facility)) }

    context 'when poll does not exist' do
      it 'returns 404 error' do
        delete '/api/v1/polls?pollId=non-existent-id', headers: headers
        expect(response).to have_http_status(404)
        expect(json_response[:error]).to eq('Poll does not exist')
        expect(json_response[:code]).to eq(99999)
      end
    end

    context 'when poll belongs to a facility' do
      it 'allows vendor from same facility to delete' do
        expect {
          delete "/api/v1/polls?pollId=#{facility_poll.id}", headers: vendor_headers
        }.to change(Poll, :count).by(-1)
        expect(response).to have_http_status(200)
      end

      it 'prevents vendor from different facility from deleting' do
        delete "/api/v1/polls?pollId=#{other_facility_poll.id}", headers: vendor_headers
        expect(response).to have_http_status(404)
        expect(json_response[:error]).to eq('You do not have permission to delete this poll')
      end

      it 'allows admin to delete any facility poll' do
        admin.update!(team: team)  # Admin needs team setup
        expect {
          delete "/api/v1/polls?pollId=#{facility_poll.id}", headers: admin_headers
        }.to change(Poll, :count).by(-1)
        expect(response).to have_http_status(200)
      end

      it 'prevents regular user from deleting facility poll' do
        delete "/api/v1/polls?pollId=#{facility_poll.id}", headers: headers
        expect(response).to have_http_status(500)  # User has no team, causes error
      end
    end

    context 'when poll is global (no facility)' do
      it 'allows admin to delete global poll' do
        expect {
          delete "/api/v1/polls?pollId=#{global_poll.id}", headers: admin_headers
        }.to change(Poll, :count).by(-1)
        expect(response).to have_http_status(200)
      end

      it 'prevents vendor from deleting global poll' do
        delete "/api/v1/polls?pollId=#{global_poll.id}", headers: vendor_headers
        expect(response).to have_http_status(404)
        expect(json_response[:error]).to eq('You do not have permission to delete this poll (admin)')
      end

      it 'prevents regular user from deleting global poll' do
        delete "/api/v1/polls?pollId=#{global_poll.id}", headers: headers
        expect(response).to have_http_status(404)
        expect(json_response[:error]).to eq('You do not have permission to delete this poll (admin)')
      end
    end

    it 'cascades deletion to options and user responses' do
      poll = create(:poll, facility: facility)
      option1 = create(:option, poll: poll)
      option2 = create(:option, poll: poll)
      response1 = create(:user_response, poll: poll, option: option1)
      response2 = create(:user_response, poll: poll, option: option2)

      expect {
        delete "/api/v1/polls?pollId=#{poll.id}", headers: vendor_headers
      }.to change(Poll, :count).by(-1)
        .and change(Option, :count).by(-2)
        .and change(UserResponse, :count).by(-2)
    end
  end

  describe 'POST /api/v1/polls/create_with_prompt' do
    let(:prompt_params) do
      {
        prompt: 'Create a poll about wine preferences',
        facilityId: facility.id
      }
    end

    before do
      # Mock the GPT helper methods
      allow_any_instance_of(GptHelper).to receive(:generate_standardized_poll_response).and_return([
        {
          'message' => {
            'content' => {
              'question' => 'What type of wine do you prefer?',
              'options' => [
                { 'option' => 'Red Wine' },
                { 'option' => 'White Wine' },
                { 'option' => 'Sparkling Wine' }
              ]
            }.to_json
          }
        }
      ])
    end

    it 'requires vendor role' do
      post '/api/v1/polls/create_with_prompt', params: prompt_params.to_json, headers: headers
      expect(response).to have_http_status(403)
      expect(json_response[:message]).to include('only vendors can access')
    end

    it 'creates poll from GPT prompt for vendor' do
      post '/api/v1/polls/create_with_prompt', params: prompt_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)

      body = json_response
      expect(body[:question]).to eq('What type of wine do you prefer?')
      expect(body[:facility_id]).to eq(facility.id)
      expect(body[:options]).to be_an(Array)
      expect(body[:options].length).to eq(3)
    end

    it 'handles GPT failure gracefully' do
      allow_any_instance_of(GptHelper).to receive(:generate_standardized_poll_response).and_return(nil)

      post '/api/v1/polls/create_with_prompt', params: prompt_params.to_json, headers: vendor_headers
      expect(response).to have_http_status(404)
      expect(json_response[:error]).to eq('Failed to generate poll options from promp')
      expect(json_response[:code]).to eq(99999)
    end

    it 'validates required parameters' do
      post '/api/v1/polls/create_with_prompt', params: { prompt: 'test' }.to_json, headers: vendor_headers
      expect(response).to have_http_status(400)
    end
  end

  describe 'GET /api/v1/polls/generate_questions' do
    before do
      # Mock the GPT helper method
      allow_any_instance_of(GptHelper).to receive(:generate_poll_questions).and_return([
        {
          'message' => {
            'content' => {
              'question' => 'What is your favorite wine region?',
              'options' => [
                { 'option' => 'Napa Valley' },
                { 'option' => 'Bordeaux' },
                { 'option' => 'Tuscany' }
              ]
            }.to_json
          }
        }
      ])
    end

    it 'requires vendor role' do
      get '/api/v1/polls/generate_questions', headers: headers
      expect(response).to have_http_status(403)
      expect(json_response[:message]).to include('only vendors can access')
    end

    it 'generates poll questions for vendor facility' do
      get '/api/v1/polls/generate_questions', headers: vendor_headers
      expect(response).to have_http_status(200)

      body = json_response
      puts "Response body: #{body.inspect}"  # Debug output
      expect(body[:question]).to eq('What is your favorite wine region?')
      expect(body[:options]).to be_an(Array)
      expect(body[:options].length).to eq(3)
    end

    it 'handles GPT failure gracefully' do
      allow_any_instance_of(GptHelper).to receive(:generate_poll_questions).and_return([])

      get '/api/v1/polls/generate_questions', headers: vendor_headers
      expect(response).to have_http_status(404)
      expect(json_response[:error]).to eq('Failed to generate questions')
      expect(json_response[:code]).to eq(99999)
    end

    it 'calls generate_poll_questions with facility name' do
      expect_any_instance_of(GptHelper).to receive(:generate_poll_questions).with(facility.name)
      get '/api/v1/polls/generate_questions', headers: vendor_headers
    end
  end

  describe 'Edge Cases and Error Handling' do
    it 'handles malformed JSON gracefully' do
      post '/api/v1/polls/create_poll',
           params: '{"question": "test", "options": [}',
           headers: vendor_headers.merge('CONTENT_TYPE' => 'application/json')
      expect(response).to have_http_status(400)
    end

    it 'handles empty options array' do
      params = {
        question: 'Test question?',
        options: []
      }
      post '/api/v1/polls/create_poll', params: params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)

      poll = Poll.last
      expect(poll.options.count).to eq(0)
    end

    it 'handles very long question' do
      long_question = 'A' * 1000
      params = {
        question: long_question,
        options: [{ option: 'Yes' }, { option: 'No' }]
      }
      post '/api/v1/polls/create_poll', params: params.to_json, headers: vendor_headers
      expect(response).to have_http_status(201)

      poll = Poll.last
      expect(poll.question).to eq(long_question)
    end
  end

  describe 'Data Integrity' do
    it 'ensures poll belongs to vendor facility after creation' do
      params = {
        question: 'Test question?',
        options: [{ option: 'Option 1' }]
      }
      post '/api/v1/polls/create_poll', params: params.to_json, headers: vendor_headers

      poll = Poll.last
      expect(poll.facility).to eq(facility)
      expect(poll.facility.team).to eq(vendor.team)
    end

    it 'maintains referential integrity when deleting polls' do
      poll = create(:poll, facility: facility)
      option = create(:option, poll: poll)
      user_response = create(:user_response, poll: poll, option: option, user: user)

      delete "/api/v1/polls?pollId=#{poll.id}", headers: vendor_headers

      expect(Poll.find_by(id: poll.id)).to be_nil
      expect(Option.find_by(id: option.id)).to be_nil
      expect(UserResponse.find_by(id: user_response.id)).to be_nil
    end
  end
end
