require 'rails_helper'

RSpec.describe 'V1 Facilities API', type: :request, api_http: true do
  let(:user) { create(:user, role: 0, verified: true) }
  let(:token) { user.generate_jwt }
  let(:headers) { json_request_headers.merge('Authorization' => "Bearer #{token}") }

  let!(:facility) { create(:facility) }

  describe 'GET /api/v1/facility' do
    it 'returns facility by id when exists' do
      get '/api/v1/facility', params: { id: facility.id }, headers: headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body[:id]).to eq(facility.id)
    end

    it 'returns 403 when facility not found' do
      get '/api/v1/facility', params: { id: 'missing' }, headers: headers
      expect(response).to have_http_status(403)
      body = json_response
      expect(body[:code]).to eq(Constants::FacilityErrors::FacilityErrors_NotFound)
    end
  end

  describe 'GET /api/v1/facility/team' do
    it 'requires vendor role' do
      get '/api/v1/facility/team', params: { team_id: 'anything' }, headers: headers
      expect(response).to have_http_status(403)
    end

    it 'returns facility by team id for vendor' do
      vendor = create(:user, role: 1, verified: true)
      vendor_headers = json_request_headers.merge('Authorization' => "Bearer #{vendor.generate_jwt}")
      team = Team.create!(name: facility.name)
      facility.update!(team: team)

      get '/api/v1/facility/team', params: { team_id: team.id }, headers: vendor_headers
      expect(response).to have_http_status(200)
      body = json_response
      expect(body[:id]).to eq(facility.id)
    end

    it 'returns 403 when facility by team not found' do
      vendor = create(:user, role: 1, verified: true)
      vendor_headers = json_request_headers.merge('Authorization' => "Bearer #{vendor.generate_jwt}")
      get '/api/v1/facility/team', params: { team_id: 'missing' }, headers: vendor_headers
      expect(response).to have_http_status(403)
      expect(json_response[:code]).to eq(Constants::FacilityErrors::FacilityErrors_NotFound)
    end
  end

  describe 'GET /api/v1/facility/near_me_popular' do
    it 'returns up to 5 facilities near location sorted by followers' do
      get '/api/v1/facility/near_me_popular', params: { lat: 40.7128, lng: -74.0060, radius: 100 }, headers: headers
      expect(response).to have_http_status(200)
      expect(json_response).to be_an(Array)
    end
  end

  describe 'GET /api/v1/facility/search' do
    it 'requires verification' do
      unverified = create(:user, role: 0, verified: false)
      get '/api/v1/facility/search', params: { term: facility.name }, headers: json_request_headers.merge('Authorization' => "Bearer #{unverified.generate_jwt}")
      expect(response).to have_http_status(403)
    end

    it 'returns paginated results for term' do
      get '/api/v1/facility/search', params: { term: facility.name }, headers: headers
      expect(response).to have_http_status(200)
      expect(json_response).to be_an(Array)
    end
  end

  describe 'GET /api/v1/facility/rewards' do
    it 'requires vendor role' do
      get '/api/v1/facility/rewards', headers: headers
      expect(response).to have_http_status(403)
    end
  end

  describe 'GET /api/v1/facility/checkins' do
    it 'requires vendor role' do
      get '/api/v1/facility/checkins', headers: headers
      expect(response).to have_http_status(403)
    end
  end

  describe 'PUT /api/v1/facility/follow_all' do
    it 'follows multiple facilities' do
      other = create(:facility)
      put '/api/v1/facility/follow_all', params: { facility_ids: [ facility.id, other.id ] }.to_json, headers: headers
      expect(response).to have_http_status(200)
      expect(user.following?(facility)).to be true
      expect(user.following?(other)).to be true
    end

    it 'returns 403 when any facility missing' do
      put '/api/v1/facility/follow_all', params: { facility_ids: [ facility.id, 'missing' ] }.to_json, headers: headers
      expect(response).to have_http_status(403)
      expect(json_response[:code]).to eq(Constants::FacilityErrors::FacilityErrors_NotFound)
    end
  end

  describe 'PUT /api/v1/facility/follow' do
    it 'follows a facility' do
      put '/api/v1/facility/follow', params: { facility_id: facility.id }.to_json, headers: headers
      expect(response).to have_http_status(200)
      expect(user.following?(facility)).to be true
    end

    it 'returns 403 if already following' do
      user.follow(facility)
      put '/api/v1/facility/follow', params: { facility_id: facility.id }.to_json, headers: headers
      expect(response).to have_http_status(403)
    end
  end

  describe 'PUT /api/v1/facility/unfollow' do
    it 'unfollows a facility' do
      user.follow(facility)
      put '/api/v1/facility/unfollow', params: { facility_id: facility.id }.to_json, headers: headers
      expect(response).to have_http_status(200)
      expect(user.following?(facility)).to be false
    end
  end
end
