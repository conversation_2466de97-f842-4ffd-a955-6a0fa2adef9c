require 'rails_helper'

RSpec.describe 'V1 Settings User API', type: :request, api_http: true do
  let(:user) { create(:user, role: 0, verified: true) }
  let(:token) { user.generate_jwt }
  let(:headers) { json_request_headers.merge('Authorization' => "Bearer #{token}") }

  describe 'PUT /api/v1/settings_user' do
    context 'when authenticated' do
      context 'with valid parameters' do
        it 'updates user gender' do
          put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.gender).to eq(1)
          
          body = json_response
          expect(body[:id]).to eq(user.id)
          expect(body[:gender]).to eq(1)
        end

        it 'updates user phone' do
          new_phone = '+1234567890'
          put '/api/v1/settings_user', params: { phone: new_phone }.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.phone).to eq(new_phone)
          
          body = json_response
          expect(body[:id]).to eq(user.id)
          expect(body[:phone]).to eq(new_phone)
        end

        it 'updates user device_token' do
          new_token = 'device_token_123'
          put '/api/v1/settings_user', params: { device_token: new_token }.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.device_token).to eq(new_token)
          
          body = json_response
          expect(body[:id]).to eq(user.id)
          expect(body[:device_token]).to eq(new_token)
        end

        it 'updates multiple attributes at once' do
          params = {
            gender: 2,
            phone: '+9876543210',
            device_token: 'multi_update_token'
          }
          
          put '/api/v1/settings_user', params: params.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.gender).to eq(2)
          expect(user.phone).to eq('+9876543210')
          expect(user.device_token).to eq('multi_update_token')
          
          body = json_response
          expect(body[:id]).to eq(user.id)
          expect(body[:gender]).to eq(2)
          expect(body[:phone]).to eq('+9876543210')
          expect(body[:device_token]).to eq('multi_update_token')
        end

        it 'ignores unknown parameters' do
          original_email = user.email
          params = {
            gender: 1,
            unknown_param: 'should_be_ignored',
            email: '<EMAIL>'
          }
          
          put '/api/v1/settings_user', params: params.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.gender).to eq(1)
          expect(user.email).to eq(original_email) # Should not change
        end

        it 'handles empty parameters gracefully' do
          put '/api/v1/settings_user', params: {}.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          body = json_response
          expect(body[:id]).to eq(user.id)
        end
      end

      context 'with avatar upload' do
        let(:avatar_file) do
          fixture_file_upload(Rails.root.join('spec', 'fixtures', 'files', 'test_avatar.jpg'), 'image/jpeg')
        end

        before do
          # Create test avatar file if it doesn't exist
          FileUtils.mkdir_p(Rails.root.join('spec', 'fixtures', 'files'))
          unless File.exist?(Rails.root.join('spec', 'fixtures', 'files', 'test_avatar.jpg'))
            # Create a minimal test image file
            File.write(Rails.root.join('spec', 'fixtures', 'files', 'test_avatar.jpg'), 
                      "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0C\x14\r\x0C\x0B\x0B\x0C\x19\x12\x13\x0F\x14\x1D\x1A\x1F\x1E\x1D\x1A\x1C\x1C $.' \",#\x1C\x1C(7),01444\x1F'9=82<.342\xFF\xC0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xFF\xC4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xFF\xC4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xDA\x00\x0C\x03\x01\x00\x02\x11\x03\x11\x00\x3F\x00\xAA\xFF\xD9")
          end
        end

        it 'updates user avatar with file upload' do
          # For file uploads, we need to use multipart form data instead of JSON
          put '/api/v1/settings_user', 
              params: { avatar: avatar_file }, 
              headers: headers.except('CONTENT_TYPE') # Remove JSON content type for file upload
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.avatar).to be_present
          
          body = json_response
          expect(body[:id]).to eq(user.id)
          expect(body[:avatar]).to be_present
        end

        it 'updates avatar along with other parameters' do
          put '/api/v1/settings_user', 
              params: { 
                avatar: avatar_file,
                gender: 1,
                phone: '+1111111111'
              }, 
              headers: headers.except('CONTENT_TYPE')
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.avatar).to be_present
          expect(user.gender).to eq(1)
          expect(user.phone).to eq('+1111111111')
        end
      end

      context 'with invalid parameters' do
        it 'handles invalid gender type gracefully' do
          put '/api/v1/settings_user', params: { gender: 'invalid' }.to_json, headers: headers
          
          # Grape should handle type validation
          expect(response).to have_http_status(400)
        end

        it 'handles nil values' do
          put '/api/v1/settings_user', params: { phone: nil }.to_json, headers: headers
          
          expect(response).to have_http_status(200)
          user.reload
          expect(user.phone).to be_nil
        end
      end
    end

    context 'when not authenticated' do
      it 'returns 401 without authorization header' do
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: json_request_headers
        
        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to include('401 Unauthorized')
      end

      it 'returns 401 with invalid token' do
        invalid_headers = json_request_headers.merge('Authorization' => 'Bearer invalid_token')
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: invalid_headers
        
        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to include('401 Unauthorized')
      end

      it 'returns 401 with expired token' do
        # Create an expired token
        expired_payload = { id: user.id, exp: 1.hour.ago.to_i }
        expired_token = JWT.encode(expired_payload, Rails.application.secret_key_base)
        expired_headers = json_request_headers.merge('Authorization' => "Bearer #{expired_token}")
        
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: expired_headers
        
        expect(response).to have_http_status(401)
        body = json_response
        expect(body[:message]).to include('401 Unauthorized')
      end
    end

    context 'with different user roles' do
      it 'works for regular users (role 0)' do
        regular_user = create(:user, role: 0, verified: true)
        regular_headers = json_request_headers.merge('Authorization' => "Bearer #{regular_user.generate_jwt}")
        
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: regular_headers
        
        expect(response).to have_http_status(200)
        regular_user.reload
        expect(regular_user.gender).to eq(1)
      end

      it 'works for vendor users (role 1)' do
        vendor_user = create(:user, role: 1, verified: true)
        vendor_headers = json_request_headers.merge('Authorization' => "Bearer #{vendor_user.generate_jwt}")
        
        put '/api/v1/settings_user', params: { phone: '+5555555555' }.to_json, headers: vendor_headers
        
        expect(response).to have_http_status(200)
        vendor_user.reload
        expect(vendor_user.phone).to eq('+5555555555')
      end

      it 'works for admin users (role 2)' do
        admin_user = create(:user, role: 2, verified: true)
        admin_headers = json_request_headers.merge('Authorization' => "Bearer #{admin_user.generate_jwt}")
        
        put '/api/v1/settings_user', params: { device_token: 'admin_token' }.to_json, headers: admin_headers
        
        expect(response).to have_http_status(200)
        admin_user.reload
        expect(admin_user.device_token).to eq('admin_token')
      end
    end

    context 'response format' do
      it 'returns user entity with proper structure' do
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: headers
        
        expect(response).to have_http_status(200)
        body = json_response
        
        # Check that response includes expected user entity fields
        expect(body).to have_key(:id)
        expect(body).to have_key(:role)
        expect(body).to have_key(:verified)
        expect(body).to have_key(:username)
        expect(body).to have_key(:avatar)
        expect(body).to have_key(:perks)
        expect(body).to have_key(:trails)
        expect(body).to have_key(:checkins)
        expect(body).to have_key(:rewards)
        expect(body).to have_key(:is_following_facility)
        
        expect(body[:id]).to eq(user.id)
        expect(body[:role]).to eq(user.role)
        expect(body[:verified]).to eq(user.verified)
        expect(body[:username]).to eq(user.username)
      end

      it 'does not expose sensitive information' do
        put '/api/v1/settings_user', params: { gender: 1 }.to_json, headers: headers
        
        expect(response).to have_http_status(200)
        body = json_response
        
        # Should not include sensitive fields
        expect(body).not_to have_key(:encrypted_password)
        expect(body).not_to have_key(:reset_password_token)
        expect(body).not_to have_key(:email) # Only exposed conditionally
      end
    end
  end
end
