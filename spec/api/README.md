# API Testing with RSpec and Grape

This directory contains tests for your Grape API endpoints. The tests are written using RSpec and follow a consistent pattern to make testing your API endpoints easy and maintainable.

## Testing Structure

The testing structure follows these conventions:

- `spec/api/v1/resources/` - Contains tests for specific API resources
- `spec/support/` - Contains helper modules and configuration for testing
- `spec/factories/` - Contains FactoryBot factories for creating test data

## How to Write API Tests

### Basic Test Structure

```ruby
require 'rails_helper'

RSpec.describe API::V1::Resources::YourResource, type: :request do
  describe 'GET /api/v1/your_resource' do
    before do
      # Setup any necessary data
      # Example: create(:your_model)

      # Make the request
      get '/api/v1/your_resource'
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end

    it 'returns the expected data' do
      expect(json_response[:data]).to be_present
      # Add more specific expectations
    end
  end

  describe 'POST /api/v1/your_resource' do
    context 'with valid parameters' do
      let(:valid_params) { { name: 'Test', description: 'Test description' } }

      before do
        post '/api/v1/your_resource', params: valid_params
      end

      it 'returns a created status' do
        expect(response).to have_http_status(:created)
      end

      it 'creates a new resource' do
        expect(json_response[:id]).to be_present
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) { { name: '' } }

      before do
        post '/api/v1/your_resource', params: invalid_params
      end

      it 'returns a bad request status' do
        expect(response).to have_http_status(:bad_request)
      end

      it 'returns error details' do
        expect(json_response[:error]).to be_present
      end
    end
  end
end
```

### Helper Methods

The `ApiHelpers` module provides several useful methods:

- `json_response` - Parses the response body as JSON with symbolized keys
- `json_request_headers` - Returns headers for JSON requests

### Testing Templates

Check out the `testing_template_spec.rb` file for a complete example of how to structure your API tests.

## Running Tests

To run all API tests:

```
bundle exec rspec spec/api
```

To run tests for a specific resource:

```
bundle exec rspec spec/api/v1/resources/your_resource_spec.rb
```

## Best Practices

1. Test both happy and sad paths
2. Test parameter validation
3. Test authentication and authorization if applicable
4. Use factories to create test data
5. Keep tests focused and isolated
6. Use descriptive test names
7. Group related tests with `context` blocks
