require 'spec_helper'
require 'grape'
require 'rack/test'

# This is a template for testing Grape APIs
# You can use this as a reference for creating your own API tests

# Define a test API class outside of the module
class TestAPI < Grape::API
  format :json

  resource :test do
    desc 'Test endpoint'
    get do
      { message: 'Test successful' }
    end

    desc 'Echo endpoint'
    params do
      requires :value, type: String, desc: 'Value to echo'
    end
    post :echo do
      { echo: params[:value] }
    end
  end
end

module RackTestHelpers
  include Rack::Test::Methods

  def app
    # Replace with your actual API class in real tests
    # Example: API::V1::Base
    TestAPI
  end

  def json_response
    JSON.parse(last_response.body, symbolize_names: true)
  end
end

RSpec.describe 'API Testing Template' do
  include RackTestHelpers

  describe 'GET /test' do
    before do
      get '/test'
    end

    it 'returns a successful response' do
      expect(last_response.status).to eq(200)
    end

    it 'returns the correct message' do
      expect(json_response[:message]).to eq('Test successful')
    end
  end

  describe 'POST /test/echo' do
    context 'with valid parameters' do
      before do
        post '/test/echo', { value: 'Hello World' }
      end

      it 'returns a successful response' do
        expect(last_response.status).to eq(201)
      end

      it 'echoes the provided value' do
        expect(json_response[:echo]).to eq('Hello World')
      end
    end

    context 'with missing parameters' do
      before do
        post '/test/echo'
      end

      it 'returns a bad request status' do
        expect(last_response.status).to eq(400)
      end

      it 'returns an error message' do
        expect(json_response[:error]).to include('value is missing')
      end
    end
  end
end
