require 'rails_helper'
require Rails.root.join('app/api/api.rb')

RSpec.describe 'Grape API Setup', type: :request do
  it 'has Grape defined' do
    expect(defined?(Grape)).to eq('constant')
  end

  it 'has Grape::API defined' do
    expect(defined?(Grape::API)).to eq('constant')
  end

  it 'has API defined' do
    expect(defined?(API)).to eq('constant')
  end

  it 'has V1::Base defined' do
    expect(defined?(V1::Base)).to eq('constant')
  end
end
