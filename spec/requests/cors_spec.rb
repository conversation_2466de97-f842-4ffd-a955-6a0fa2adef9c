require 'rails_helper'

RSpec.describe 'CORS', type: :request, cors: true do
  before(:all) do
    Rails.application.reload_routes!
  end

  it 'allows requests from allowed origins' do
    get '/api/v1/hello', headers: { 'Origin' => 'http://localhost:3000' }
    puts "Response: ", response.inspect
    puts "Headers: ", response.headers.inspect
    expect(response.headers['Access-Control-Allow-Origin']).to eq('http://localhost:3000')
    expect(response.headers['Access-Control-Allow-Methods']).to include('GET')
  end

  it 'includes the exposed headers' do
    get '/api/v1/hello', headers: { 'Origin' => 'http://localhost:3000' }
    puts "Response: ", response.inspect
    puts "Headers: ", response.headers.inspect
    expect(response.headers['Access-Control-Expose-Headers']).to include('Authorization')
    expect(response.headers['Access-Control-Expose-Headers']).to include('X-Total-Count')
    expect(response.headers['Access-Control-Expose-Headers']).to include('X-Total-Pages')
  end

  it 'handles preflight OPTIONS requests' do
    options '/api/v1/hello', headers: {
      'Origin' => 'http://localhost:3000',
      'Access-Control-Request-Method' => 'GET',
      'Access-Control-Request-Headers' => 'Content-Type,Authorization'
    }
    puts "Response: ", response.inspect
    puts "Headers: ", response.headers.inspect
    expect([ 200, 204 ]).to include(response.status) # Accept either 200 or 204
    expect(response.headers['Access-Control-Allow-Origin']).to eq('http://localhost:3000')
    expect(response.headers['Access-Control-Allow-Methods']).to include('GET')
    expect(response.headers['Access-Control-Allow-Headers']).to include('Content-Type')
    expect(response.headers['Access-Control-Allow-Headers']).to include('Authorization')
    expect(response.headers['Access-Control-Max-Age']).to eq('86400')
  end
end
