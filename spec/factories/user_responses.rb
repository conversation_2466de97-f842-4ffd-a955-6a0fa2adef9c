FactoryBot.define do
  factory :user_response do
    association :user
    association :poll
    association :option

    # Ensure the option belongs to the same poll
    after(:build) do |user_response|
      if user_response.option && user_response.poll && user_response.option.poll != user_response.poll
        user_response.option = create(:option, poll: user_response.poll)
      end
    end
  end
end
