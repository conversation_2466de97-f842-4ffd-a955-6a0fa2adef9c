FactoryBot.define do
  factory :user do
    sequence(:email) { |n| "user#{n}@example.com" }
    sequence(:username) { |n| "user#{n}" }
    password { "password123" }
    password_confirmation { "password123" }
    verified { true }
    role { 0 } # Default to regular user

    trait :unverified do
      verified { false }
    end

    trait :vendor do
      role { 1 }
    end

    trait :admin do
      role { 2 }
    end
  end
end
