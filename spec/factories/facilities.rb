FactoryBot.define do
  factory :facility do
    sequence(:name) { |n| "Facility #{n}" }
    sequence(:handle) { |n| "facility_#{n}" }
    latitude { 40.7128 }
    longitude { -74.0060 }
    street { "123 Main St" }
    city { "New York" }
    state { "NY" }
    zipcode { "10001" }
    country { "USA" }
    brewery_type { "brewery" }

    trait :winery do
      brewery_type { "winery" }
    end

    trait :distillery do
      brewery_type { "distillery" }
    end
  end
end
