FactoryBot.define do
  factory :trail do
    sequence(:name) { |n| "Trail #{n}" }
    description { "A test trail" }
    startdate { 1.day.ago }
    enddate { 1.day.from_now }
    code { nil }
    is_active { true }
    latitude { 40.7128 }
    longitude { -74.0060 }
    should_verify_location { false }
    should_verify_time_between { false }
    should_verify_user_verified { false }

    trait :completed do
      startdate { 2.days.ago }
      enddate { 1.day.ago }
    end

    trait :active do
      startdate { 1.day.ago }
      enddate { 1.day.from_now }
    end

    trait :future do
      startdate { 1.day.from_now }
      enddate { 2.days.from_now }
    end
  end
end
