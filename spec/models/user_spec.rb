# spec/models/user_spec.rb
require 'rails_helper'
require 'jwt'

RSpec.describe 'smoke' do
    it 'loads this file' do
      expect(true).to be true
    end
  end

RSpec.describe User, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  let(:user) { create(:user) }

  describe '#generate_jwt' do
    around do |example|
      travel_to Time.zone.local(2025, 5, 22, 12, 0, 0) { example.run }
      travel_back
    end

    it 'encodes the user id and 24-hour expiry into the token' do
      token   = user.generate_jwt
      payload = JWT.decode(token, nil, false).first

      expect(payload['id']).to eq(user.id)
      expect(payload['exp']).to eq((Time.zone.now + 24.hours).to_i)
    end
  end

  describe 'role predicate methods' do
    it 'identifies a sipper when role is 0' do
      user.update!(role: 0)
      expect(user.isSipper).to be true
      expect(user.isVendor).to be false
      expect(user.isAdmin).to be false
      expect(user.admin?).to be false
    end

    it 'identifies a vendor when role is 1' do
      user.update!(role: 1)
      expect(user.isVendor).to be true
      expect(user.isSipper).to be false
      expect(user.isAdmin).to be false
    end

    it 'identifies an admin when role is 2' do
      user.update!(role: 2)
      expect(user.isAdmin).to be true
      expect(user.admin?).to be true
      expect(user.isSipper).to be false
      expect(user.isVendor).to be false
    end
  end

  describe '#verify' do
    let!(:verification) { create(:verification, user: user) }

    before do
      user.update!(verified: false)
    end

    it 'marks user as verified and deletes the verification record' do
      expect {
        user.verify
      }.to change { user.reload.verified }.from(false).to(true)
         .and change { Verification.where(id: verification.id).exists? }.from(true).to(false)
    end
  end

  describe '#create_verification' do
    context 'when no verification exists' do
      it 'creates a new verification with a 6-digit code' do
        expect {
          user.create_verification
        }.to change(Verification, :count).by(1)

        new_ver = Verification.find_by(user_id: user.id)
        expect(new_ver.code).to be_between(100_000, 999_999)
      end
    end

    context 'when a verification already exists' do
      let!(:existing) { create(:verification, user: user, code: 123_456) }

      it 'does not create a new record but updates the code' do
        expect {
          user.create_verification
        }.not_to change(Verification, :count)

        existing.reload
        expect(existing.code).not_to eq(123_456)
        expect(existing.code).to be_between(100_000, 999_999)
      end
    end
  end

  describe '#addReward' do
    before do
      allow(TrailDetermineWinnerJob).to receive_message_chain(:set, :perform_later)
    end

    let(:user) { create(:user) }
    let(:trail) { create(:trail) }
    let(:reward) { create(:reward, trail: trail, claimed_by: nil) }

    it 'assigns the reward to the user' do
      user.addReward(reward)
      expect(reward.reload.claimed_by).to eq(user.id)
    end
  end

  describe 'facility following methods' do
    let(:facility) { create(:facility) }

    it 'can follow and unfollow a facility and query following status' do
      expect(user.isFollowingAFacility).to be false
      expect(user.following?(facility)).to be false

      expect {
        user.follow(facility)
      }.to change { user.following_facilities.count }.by(1)

      expect(user.isFollowingAFacility).to be true
      expect(user.following?(facility)).to be true

      expect {
        user.unfollow(facility)
      }.to change { user.following_facilities.count }.by(-1)

      expect(user.following?(facility)).to be false
    end
  end

  describe '#joined?' do
    before do
      allow(TrailDetermineWinnerJob).to receive_message_chain(:set, :perform_later)
    end

    let(:trail) { create(:trail) }

    it 'returns true if the user has joined the trail' do
      expect(user.joined?(trail)).to be false
      user.trails << trail
      expect(user.joined?(trail)).to be true
    end
  end
end
