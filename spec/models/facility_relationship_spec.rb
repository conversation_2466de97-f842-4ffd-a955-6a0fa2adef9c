require 'rails_helper'

RSpec.describe FacilityRelationship, type: :model do
  describe 'associations' do
    it { should belong_to(:follower).class_name('User') }
    it { should belong_to(:facility).class_name('Facility') }
  end

  describe 'validations' do
    it { should validate_presence_of(:follower_id) }
    it { should validate_presence_of(:facility_id) }
  end

  describe 'factory' do
    it 'is invalid without a follower' do
      relationship = build(:facility_relationship, follower: nil)
      expect(relationship).not_to be_valid
    end

    it 'is invalid without a facility' do
      relationship = build(:facility_relationship, facility: nil)
      expect(relationship).not_to be_valid
    end
  end
end
