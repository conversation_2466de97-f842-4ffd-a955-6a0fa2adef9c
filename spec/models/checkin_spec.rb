require 'rails_helper'

RSpec.describe Checkin, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:facility) }
    it { should belong_to(:trail) }
  end

  describe 'validations' do
    it 'is valid with valid attributes' do
      checkin = build(:checkin)
      expect(checkin).to be_valid
    end

    it 'is invalid without a user' do
      checkin = build(:checkin, user: nil)
      expect(checkin).not_to be_valid
    end

    it 'is invalid without a facility' do
      checkin = build(:checkin, facility: nil)
      expect(checkin).not_to be_valid
    end

    it 'is invalid without a trail' do
      checkin = build(:checkin, trail: nil)
      expect(checkin).not_to be_valid
    end
  end
end
