require 'rails_helper'

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

RSpec.describe Trail, type: :model do
  before do
    allow(TrailDetermineWinnerJob).to receive_message_chain(:set, :perform_later)
  end

  describe '#schedule_determine_winner_job' do
    let(:trail) { create(:trail) }

    it 'schedules a TrailDetermineWinnerJob for the trail enddate' do
      expect(TrailDetermineWinnerJob).to receive(:set)
        .with(wait_until: trail.enddate)
        .and_return(double(perform_later: true))

      trail.schedule_determine_winner_job
    end

    it 'is called after a trail is created' do
      new_trail = build(:trail)
      expect(new_trail).to receive(:schedule_determine_winner_job)

      new_trail.save
    end
  end

  describe 'associations' do
    it { should have_and_belong_to_many(:facilities) }
    it { should have_many(:rewards) }
    it { should have_and_belong_to_many(:users) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:startdate) }
    it { should validate_presence_of(:enddate) }
  end

  describe '#verifiesLocationOnCheckin' do
    it 'returns should_verify_location' do
      trail = build(:trail, should_verify_location: true)
      expect(trail.verifiesLocationOnCheckin).to eq(true)
      trail.should_verify_location = false
      expect(trail.verifiesLocationOnCheckin).to eq(false)
    end
  end

  describe '#verifiesTimeFromLastCheckin' do
    it 'returns should_verify_time_between' do
      trail = build(:trail, should_verify_time_between: true)
      expect(trail.verifiesTimeFromLastCheckin).to eq(true)
      trail.should_verify_time_between = false
      expect(trail.verifiesTimeFromLastCheckin).to eq(false)
    end
  end

  describe '#verifiesUserIsVerifiedOnCheckin' do
    it 'returns should_verify_user_verified' do
      trail = build(:trail, should_verify_user_verified: true)
      expect(trail.verifiesUserIsVerifiedOnCheckin).to eq(true)
      trail.should_verify_user_verified = false
      expect(trail.verifiesUserIsVerifiedOnCheckin).to eq(false)
    end
  end

  describe '#hostFacility' do
    it 'returns the facility with the id matching host' do
      facility = create(:facility)
      trail = build(:trail, host: facility.id)
      expect(trail.hostFacility).to eq(facility)
    end

    it 'returns nil if no facility matches host' do
      trail = build(:trail, host: -1)
      expect(trail.hostFacility).to be_nil
    end
  end

  describe '#generate_token_if_needed' do
    it 'generates a code if code is nil' do
      trail = create(:trail, code: nil)
      expect(trail.code).not_to be_nil
    end

    it 'returns code if already present' do
      trail = create(:trail, code: 1234567)
      expect(trail.code).to eq(1234567)
    end
  end

  describe '#token' do
    it 'returns a base64 encoded JSON with code and trail_id' do
      trail = create(:trail, code: 1234567)
      trail.update!(code: 1234567)
      decoded = JSON.parse(Base64.decode64(trail.token))
      expect(decoded['code']).to eq(1234567)
      expect(decoded['trail_id']).to eq(trail.id)
    end
  end
end
