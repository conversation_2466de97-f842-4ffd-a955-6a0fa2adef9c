require 'rails_helper'

RSpec.describe Checkin<PERSON>elper do
  before do
    allow_any_instance_of(Trail).to receive(:schedule_determine_winner_job)
  end

  let(:helper_class) do
    Class.new do
      include Checkin<PERSON><PERSON>per

      def error!(message, status)
        raise Grape::Exceptions::Base.new(message: message, status: status)
      end

      def current_user
        @current_user ||= FactoryBot.create(:user)
      end

      def params
        { token: Base64.encode64(token_info.to_json) }
      end

      def token_info
        @token_info ||= {
          'id' => facility.id,
          'code' => facility.code,
          'datetime' => DateTime.now.to_s
        }
      end

      def facility
        @facility ||= FactoryBot.create(:facility, latitude: 40.7128, longitude: -74.0060, radius: 1.0, code: 123456, last_code_update: DateTime.now)
      end

      def trail
        @trail ||= FactoryBot.create(:trail,
          startdate: DateTime.now - 1.day,
          enddate: DateTime.now + 1.day,
          should_verify_user_verified: true,
          should_verify_location: true,
          should_verify_time_between: true
        )
      end
    end
  end

  let(:helper) { helper_class.new }

  describe '#validate_facility_checkin' do
    context 'when facility is nil' do
      it 'raises an error' do
        allow(helper).to receive(:facility).and_return(nil)
        allow(helper).to receive(:token_info).and_return({ id: nil, code: nil, datetime: DateTime.now.to_s })
        expect {
          helper.validate_facility_checkin(helper.token_info, nil)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when facility exists' do
      it 'validates user verification' do
        expect(helper).to receive(:validate_checkin_user_verified)
        expect(helper).to receive(:validate_checkin_location)
        expect(helper).to receive(:validate_checkin_date_time)

        helper.validate_facility_checkin(helper.token_info, helper.facility)
      end
    end
  end

  describe '#validate_trail_checkin' do
    context 'when facility is nil' do
      it 'raises an error' do
        allow(helper).to receive(:token_info).and_return({ id: nil, code: nil, datetime: DateTime.now.to_s })
        expect {
          helper.validate_trail_checkin(helper.token_info, nil, helper.trail)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when trail is nil' do
      it 'raises an error' do
        expect {
          helper.validate_trail_checkin(helper.token_info, helper.facility, nil)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when both facility and trail exist' do
      it 'validates all required checks' do
        expect(helper).to receive(:validate_checkin_user_verified)
        expect(helper).to receive(:validate_checkin_location)
        expect(helper).to receive(:validate_checkin_date_time)
        expect(helper).to receive(:validate_checkin_in_trail_time)

        helper.validate_trail_checkin(helper.token_info, helper.facility, helper.trail)
      end
    end
  end

  describe '#validate_checkin_token' do
    context 'with valid token' do
      it 'returns parsed token info' do
        result = helper.validate_checkin_token
        expect(result).to be_a(Hash)
        expect(result['id']).to eq(helper.facility.id)
      end
    end

    context 'with invalid token' do
      it 'returns nil' do
        allow(helper).to receive(:params).and_return({ token: 'invalid' })
        expect(helper.validate_checkin_token).to be_nil
      end
    end
  end

  describe '#validate_checkin_user_verified' do
    context 'when user is verified' do
      before do
        allow(helper.current_user).to receive(:verified?).and_return(true)
      end

      it 'returns true' do
        expect(helper.validate_checkin_user_verified).to be true
      end
    end

    context 'when user is not verified' do
      before do
        allow(helper.current_user).to receive(:verified?).and_return(false)
      end

      it 'raises an error' do
        expect {
          helper.validate_checkin_user_verified
        }.to raise_error(Grape::Exceptions::Base)
      end
    end
  end

  describe '#validate_checkin_facility' do
    context 'with valid facility info' do
      it 'does not raise an error' do
        expect {
          helper.validate_checkin_facility(helper.token_info, helper.facility)
        }.not_to raise_error
      end
    end

    context 'with invalid facility id' do
      it 'raises an error' do
        token_info = helper.token_info.merge('id' => 999)
        expect {
          helper.validate_checkin_facility(token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'with invalid facility code' do
      it 'raises an error' do
        token_info = helper.token_info.merge('code' => 'invalid')
        expect {
          helper.validate_checkin_facility(token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end
  end

  describe '#validate_checkin_location' do
    let(:locations) { FactoryBot.create_list(:location, 30, user: helper.current_user) }

    context 'with no recent locations' do
      it 'raises an error' do
        expect {
          helper.validate_checkin_location(helper.token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'with insufficient locations' do
      before do
        FactoryBot.create_list(:location, 29, user: helper.current_user)
      end

      it 'raises an error' do
        expect {
          helper.validate_checkin_location(helper.token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'with valid locations' do
      before do
        locations.each do |location|
          location.update(
            latitude: helper.facility.latitude + 0.001,
            longitude: helper.facility.longitude + 0.001,
            timestamp: DateTime.now - 1.minute
          )
        end
      end

      it 'does not raise an error' do
        expect {
          helper.validate_checkin_location(helper.token_info, helper.facility)
        }.not_to raise_error
      end
    end

    context 'with locations too far from facility' do
      before do
        locations.each do |location|
          location.update(
            latitude: helper.facility.latitude + 2.0,
            longitude: helper.facility.longitude + 2.0,
            timestamp: DateTime.now - 1.minute
          )
        end
      end

      it 'raises an error' do
        expect {
          helper.validate_checkin_location(helper.token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end
  end

  describe '#validate_checkin_in_trail_time' do
    context 'when datetime is not today' do
      it 'raises an error' do
        token_info = helper.token_info.merge('datetime' => (DateTime.now - 1.day).to_s)
        expect {
          helper.validate_checkin_in_trail_time(token_info, helper.facility, helper.trail)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when outside trail time' do
      let(:expired_trail) { FactoryBot.create(:trail, startdate: DateTime.now - 2.days, enddate: DateTime.now - 1.day) }

      it 'raises an error' do
        token_info = helper.token_info.merge('datetime' => DateTime.now.to_s)
        expect {
          helper.validate_checkin_in_trail_time(token_info, helper.facility, expired_trail)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when within trail time' do
      it 'does not raise an error' do
        token_info = helper.token_info.merge('datetime' => DateTime.now.to_s)
        expect {
          helper.validate_checkin_in_trail_time(token_info, helper.facility, helper.trail)
        }.not_to raise_error
      end
    end
  end

  describe '#validate_checkin_date_time' do
    context 'when datetime is nil' do
      it 'raises an error' do
        token_info = helper.token_info.merge('datetime' => nil)
        expect {
          helper.validate_checkin_date_time(token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when checkin is too frequent' do
      before do
        FactoryBot.create(:checkin, user: helper.current_user, created_at: DateTime.now - 5.minutes)
      end

      it 'raises an error' do
        token_info = helper.token_info.merge('datetime' => DateTime.now.to_s)
        expect {
          helper.validate_checkin_date_time(token_info, helper.facility)
        }.to raise_error(Grape::Exceptions::Base)
      end
    end

    context 'when checkin is not too frequent' do
      before do
        FactoryBot.create(:checkin, user: helper.current_user, created_at: DateTime.now - 20.minutes)
      end

      it 'does not raise an error' do
        token_info = helper.token_info.merge('datetime' => DateTime.now.to_s)
        expect {
          helper.validate_checkin_date_time(token_info, helper.facility)
        }.not_to raise_error
      end
    end

    context 'when no previous checkins' do
      it 'does not raise an error' do
        token_info = helper.token_info.merge('datetime' => DateTime.now.to_s)
        expect {
          helper.validate_checkin_date_time(token_info, helper.facility)
        }.not_to raise_error
      end
    end
  end
end
