require 'rails_helper'

RSpec.describe <PERSON>pt<PERSON>elper, type: :helper do
  let(:helper) { Class.new { include GptHelper }.new }

  before do
    # Stub environment variables
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:[]).with('GPT_KEY').and_return('test_key')
  end

  describe '#generate_gpt_auto_poll_response' do
    it 'generates a poll response' do
      # Create a temporary Poll class for testing
      poll_class = Class.new do
        def self.pluck(*)
          [ 'Question 1', 'Question 2' ]
        end
      end
      stub_const('Poll', poll_class)

      allow(helper).to receive(:generate_gpt_poll_response).and_return([ 'Generated Question' ])
      expect(helper.generate_gpt_auto_poll_response).to eq([ 'Generated Question' ])
    end
  end

  describe '#generate_poll_questions' do
    it 'generates poll questions for a brewery' do
      allow(helper).to receive(:generate_gpt_response).and_return({ 'choices' => [ 'Question 1', 'Question 2' ] })
      expect(helper.generate_poll_questions('Brewery Name')).to eq([ 'Question 1', 'Question 2' ])
    end
  end

  describe '#generate_standardized_poll_response' do
    it 'generates a standardized poll response' do
      allow(helper).to receive(:generate_gpt_poll_response).and_return([ 'Standardized Question' ])
      expect(helper.generate_standardized_poll_response('Prompt')).to eq([ 'Standardized Question' ])
    end
  end

  describe '#generate_gpt_poll_response' do
    it 'generates a GPT poll response' do
      allow(helper).to receive(:generate_gpt_response).and_return({ 'choices' => [ 'Poll Question' ] })
      expect(helper.generate_gpt_poll_response('Prompt')).to eq([ 'Poll Question' ])
    end
  end

  describe '#generate_gpt_response' do
    it 'handles client being nil' do
      allow(ENV).to receive(:[]).with('GPT_KEY').and_return(nil)
      expect(helper.generate_gpt_response('Prompt')).to be_nil
    end

    it 'handles successful API call' do
      mock_client = instance_double(ChatGPT::Client)
      allow(ChatGPT::Client).to receive(:new).and_return(mock_client)
      allow(mock_client).to receive(:chat).and_return({ 'choices' => [ 'Response' ] })

      expect(helper.generate_gpt_response('Prompt')).to eq({ 'choices' => [ 'Response' ] })
    end

    it 'handles API call error' do
      mock_client = instance_double(ChatGPT::Client)
      allow(ChatGPT::Client).to receive(:new).and_return(mock_client)
      allow(mock_client).to receive(:chat).and_raise(StandardError.new('API Error'))

      expect(helper.generate_gpt_response('Prompt')).to be_nil
    end
  end
end
