require 'rails_helper'

RSpec.describe TrailDetermineWinnerJob, type: :job do
  include ActiveJob::TestHelper

  before do
    allow_any_instance_of(Trail).to receive(:schedule_determine_winner_job)
  end

  describe '#perform' do
    let(:trail) { create(:trail, :completed) }
    let(:facilities) { create_list(:facility, 3) }
    let(:users) { create_list(:user, 3) }
    let!(:reward) { create(:reward, trail: trail) }
    let!(:single_reward) { create(:reward, :single_reward, trail: trail) }

    before do
      # Associate facilities with the trail
      trail.facilities << facilities

      # Enable ActiveRecord for this test
      allow(Rails.logger).to receive(:info)
      allow(Rails.logger).to receive(:warn)
      allow(Rails.logger).to receive(:error)
    end

    context 'when the trail does not exist' do
      it 'discards the job' do
        expect {
          perform_enqueued_jobs do
            TrailDetermineWinnerJob.perform_later(999)
          end
        }.not_to raise_error
      end
    end

    context 'when the trail has not ended yet' do
      let(:active_trail) { create(:trail, :active) }

      it 'logs a warning and returns early' do
        expect(Rails.logger).to receive(:warn).with(/has not ended yet/)

        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(active_trail.id)
        end
      end
    end

    context 'when no users have completed the trail' do
      it 'logs that no users completed the trail and returns early' do
        expect(Rails.logger).to receive(:info).with(/No users completed trail/)

        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end
      end
    end

    context 'when some users have completed the trail' do
      before do
        # Associate users with the trail
        trail.users << users

        # Create checkins for the first user at all facilities during the trail period
        facilities.each do |facility|
          create(:checkin, user: users[0], facility: facility, trail: trail,
                 created_at: trail.startdate + 1.hour)
        end

        # Create checkins for the second user at all facilities, but one is outside the trail period
        facilities[0..1].each do |facility|
          create(:checkin, user: users[1], facility: facility, trail: trail,
                 created_at: trail.startdate + 1.hour)
        end
        create(:checkin, user: users[1], facility: facilities.last, trail: trail,
               created_at: trail.enddate + 1.day) # Outside trail period

        # Third user only checks in at 2 of 3 facilities
        facilities[0..1].each do |facility|
          create(:checkin, user: users[2], facility: facility, trail: trail,
                 created_at: trail.startdate + 1.hour)
        end
      end

      it 'only counts users who checked in at all facilities during the trail period' do
        expect(Rails.logger).to receive(:info).with(/1 users completed trail/)

        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end
      end

      it 'assigns regular rewards to all users who completed the trail' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        # The regular reward should be claimed by the first user
        expect(reward.reload.is_claimed).to be true
        expect(reward.claimed_by).to eq(users[0].id)
      end

      it 'randomly assigns single rewards to one user who completed the trail' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        # The single reward should be claimed by the first user (only completer)
        expect(single_reward.reload.is_claimed).to be true
        expect(single_reward.claimed_by).to eq(users[0].id)
      end
    end

    context 'when multiple users have completed the trail' do
      let(:completers) { create_list(:user, 2) }

      before do
        # Associate users with the trail
        trail.users << completers

        # Create checkins for both users at all facilities during the trail period
        completers.each do |user|
          facilities.each do |facility|
            create(:checkin, user: user, facility: facility, trail: trail,
                   created_at: trail.startdate + 1.hour)
          end
        end
      end

      it 'assigns regular rewards to all users who completed the trail' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        # For regular rewards, all completers should get a copy
        expect(Reward.where(name: reward.name).count).to eq(2)

        # Check that both users got a reward
        completer_ids = completers.map(&:id)
        reward_user_ids = Reward.where(name: reward.name).pluck(:claimed_by)
        expect(reward_user_ids.sort).to eq(completer_ids.sort)
      end

      it 'randomly assigns single rewards to one user who completed the trail' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        # The single reward should be claimed by exactly one user
        expect(single_reward.reload.is_claimed).to be true
        expect(completers.map(&:id)).to include(single_reward.claimed_by)
      end
    end

    context 'when a reward is already claimed' do
      let!(:claimed_reward) { create(:reward, :claimed, trail: trail) }
      let(:completer) { create(:user) }

      before do
        # Associate user with the trail
        trail.users << completer

        # Create checkins for the user at all facilities during the trail period
        facilities.each do |facility|
          create(:checkin, user: completer, facility: facility, trail: trail,
                 created_at: trail.startdate + 1.hour)
        end
      end

      it 'skips already claimed rewards' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        # The claimed reward should still have the original claimed_by value
        expect(claimed_reward.reload.claimed_by).not_to eq(completer.id)
      end
    end

    context 'when an error occurs during processing' do
      before do
        allow(Trail).to receive(:find).and_raise(StandardError.new("Test error"))
      end

      it 'logs the error and re-raises it (pending due to inline adapter limitation)' do
        skip 'Error handling cannot be tested with the inline adapter.'
      end
    end

    context 'when database errors occur' do
      before do
        allow(Trail).to receive(:find).and_raise(ActiveRecord::StatementInvalid.new("Database error"))
      end

      it 'retries the job on database errors (pending due to inline adapter limitation)' do
        skip 'Retry logic cannot be tested with the inline adapter.'
      end
    end

    context 'when a trail has no facilities' do
      let(:empty_trail) { create(:trail, :completed) }

      it 'handles trails with no facilities gracefully' do
        expect(Rails.logger).to receive(:info).with(/No users completed trail/)

        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(empty_trail.id)
        end
      end
    end

    context 'when checkins are exactly at trail boundaries' do
      let(:boundary_user) { create(:user) }

      before do
        trail.users << boundary_user

        # Create checkins exactly at start and end times
        facilities.each do |facility|
          create(:checkin, user: boundary_user, facility: facility, trail: trail,
                 created_at: trail.startdate)
          create(:checkin, user: boundary_user, facility: facility, trail: trail,
                 created_at: trail.enddate)
        end
      end

      it 'includes checkins at exact start and end times' do
        expect(Rails.logger).to receive(:info).with(/1 users completed trail/)

        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end
      end
    end

    context 'when a trail has multiple rewards of the same type' do
      let!(:additional_reward) { create(:reward, trail: trail) }
      let(:completer) { create(:user) }

      before do
        trail.users << completer

        facilities.each do |facility|
          create(:checkin, user: completer, facility: facility, trail: trail,
                 created_at: trail.startdate + 1.hour)
        end
      end

      it 'assigns all regular rewards to the completer' do
        perform_enqueued_jobs do
          TrailDetermineWinnerJob.perform_later(trail.id)
        end

        expect(reward.reload.is_claimed).to be true
        expect(additional_reward.reload.is_claimed).to be true
        expect(reward.claimed_by).to eq(completer.id)
        expect(additional_reward.claimed_by).to eq(completer.id)
      end
    end
  end
end
