module JobHelpers
  # Helper method to perform a job synchronously
  def perform_job(job_class, *args)
    job_class.new.perform(*args)
  end

  # Helper method to enqueue a job and then perform it
  def enqueue_and_perform_job(job_class, *args)
    job = job_class.perform_later(*args)
    perform_enqueued_jobs
    job
  end
end

RSpec.configure do |config|
  config.include JobHelpers, type: :job
end
