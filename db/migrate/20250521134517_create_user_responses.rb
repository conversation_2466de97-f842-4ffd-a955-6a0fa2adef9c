class CreateUserResponses < ActiveRecord::Migration[8.0]
  def change
    create_table :user_responses, id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
      t.uuid :user_id, null: false
      t.uuid :poll_id, null: false
      t.uuid :option_id, null: false
      t.timestamps precision: 6, null: false
    end

    add_index :user_responses, :option_id
    add_index :user_responses, :poll_id
    add_index :user_responses, :user_id
  end
end
